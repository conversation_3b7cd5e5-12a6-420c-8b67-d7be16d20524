{"name": "Browse & PDP NZ", "created": "2025-06-17 15:53:38", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3384ms", "package_id": "env[appid]", "timestamp": 1746597492636, "type": "restartApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "1690ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "tap"}, {"action_id": "eHvkAVake5", "executionTime": "1155ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"txtShopMenuTitle\"]", "timeout": 20, "timestamp": 1748163101226, "type": "waitTill"}, {"action_id": "RbNtEW6N9T", "double_tap": false, "executionTime": "2589ms", "text_to_find": "Toys", "timeout": 30, "timestamp": 1746830828429, "type": "tapOnText"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "2639ms", "text_to_find": "Latest", "timeout": 30, "timestamp": 1746830873534, "type": "tapOnText"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2506ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1747460148720, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OmKfD9iBjD", "executionTime": "1534ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " (//XCUIElementTypeButton[contains(@name,\"bag Add\")])[1]/parent::*", "timeout": 30, "timestamp": 1746835134218, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "1957ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "zWrzEgdH3Q", "executionTime": "1377ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1748426183673, "type": "waitTill"}, {"action_id": "YbamBpASJi", "executionTime": "2738ms", "image_filename": "env[product-share-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1750040370273, "type": "tap"}, {"action_id": "83tV9A4NOn", "executionTime": "1419ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Check out \")]", "timeout": 10, "timestamp": 1746832013286, "type": "exists"}, {"action_id": "gekNSY5O2E", "executionTime": "2037ms", "locator_type": "image", "locator_value": "product-share-logo.png", "timeout": 10, "timestamp": 1746832134191, "type": "exists"}, {"action_id": "s0WyiD1w0B", "executionTime": "2883ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1746832157907, "type": "tap"}, {"action_id": "F9UfvzyNii", "executionTime": "1784ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Add to bag\"]", "method": "locator", "timeout": 10, "timestamp": 1748167542083, "type": "tap"}, {"action_id": "pk2DLZFBmx", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4396ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Learn more about AfterPay", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1746832294456, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "DhWa2PCBXE", "executionTime": "2754ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Afterpay – Now available in store\"]", "method": "locator", "timeout": 30, "timestamp": 1746832373193, "type": "tapOnText", "text_to_find": "more", "double_tap": false}, {"action_id": "F4NGh9HrLw", "executionTime": "1765ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746833833911, "type": "tap"}, {"action_id": "yEga5MkcRe", "executionTime": "1604ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 15, "timestamp": 1746833889101, "type": "tap"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "2191ms", "function_name": "text", "text": "Kid toy", "timestamp": 1746833970065, "type": "iosFunctions"}, {"action_id": "fPX582qHkp", "executionTime": "2033ms", "locator_type": "image", "locator_value": "search-result-test-se.png", "timeout": 20, "timestamp": 1746834094385, "type": "exists"}, {"action_id": "ky6rfmPv0u", "executionTime": "2228ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1750040062620, "type": "tap"}, {"action_id": "92tKl3T5N8", "executionTime": "2159ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1750040078981, "type": "tap"}, {"action_id": "Xr6F8gdd8q", "executionTime": "1609ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "method": "locator", "timeout": 15, "timestamp": 1746834621258, "type": "tap"}, {"action_id": "vfwUVEyq6X", "executionTime": "1022ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeImage[@name=\"More\"]", "timeout": 20, "timestamp": 1746834702853, "type": "exists"}, {"action_id": "QPKR6jUF9O", "executionTime": "1021ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Scan barcode\"]", "timeout": 20, "timestamp": 1746834725468, "type": "exists"}, {"action_id": "5Gj5mgIxVu", "executionTime": "2293ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1750040074903, "type": "tap"}, {"action_id": "Ey8MUB57vM", "executionTime": "3256ms", "package_id": "env[appid]", "timestamp": 1747461979110, "type": "restartApp"}, {"action_id": "DY8MfL0wXI", "double_tap": false, "executionTime": "3907ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1747462039676, "type": "tapOnText"}, {"action_id": "iwqbVl90WJ", "enter": true, "executionTime": "2200ms", "function_name": "text", "text": "notebook", "timestamp": 1747462173824, "type": "iosFunctions"}, {"action_id": "KlfYmNjrq8", "executionTime": "1486ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 20, "timestamp": 1748165970913, "type": "waitTill"}, {"action_id": "kz9lnCdwoH", "executionTime": "1955ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 15, "timestamp": 1747462204290, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "1939ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746834764179, "type": "tap"}, {"action_id": "ZCsqeOXrY1", "executionTime": "1337ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "timeout": 30, "timestamp": 1746834860007, "type": "waitTill"}, {"action_id": "2p13JoJbbA", "executionTime": "1726ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "2p13JoJbbA", "executionTime": "1730ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1747462272296, "type": "tap"}, {"action_id": "x4yLCZHaCR", "executionTime": "1100ms", "package_id": "env[appid]", "timestamp": 1746834909467, "type": "terminateApp"}], "labels": [], "updated": "2025-06-17 15:53:38"}