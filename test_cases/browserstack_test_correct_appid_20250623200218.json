{"name": "browserstack_test_correct_appid", "created": "2025-06-23 22:23:28", "device_id": null, "actions": [{"action_id": "4DW5Pg01pk", "package_id": "com.saucelabs.SwagLabsMobileApp", "timestamp": 1750672694950, "type": "restartApp"}, {"action_id": "JmuCQbTn2a", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"test-Username\"]", "method": "locator", "timeout": 10, "timestamp": 1750672726610, "type": "tap"}, {"action_id": "fifVZSCEwL", "text": "standard_user", "timestamp": 1750672754512, "type": "text"}, {"action_id": "ecGfLOHDJK", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"test-Password\"]", "method": "locator", "timeout": 10, "timestamp": 1750672782983, "type": "tap"}, {"action_id": "hEBr6m1eZL", "text": "secret_sauce", "timestamp": 1750672789075, "type": "text"}, {"action_id": "YiQRCpuzmx", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "test-LOGIN", "method": "locator", "timeout": 10, "timestamp": 1750672860551, "type": "tap"}, {"action_id": "GbxajwINqL", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-Cart\"]/XCUIElementTypeOther", "timeout": 10, "timestamp": 1750672897124, "type": "exists"}], "labels": [], "updated": "2025-06-23 22:24:22"}