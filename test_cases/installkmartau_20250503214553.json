{"name": "install-kmart-au", "created": "2025-05-04 20:09:16", "device_id": "00008030-00020C123E60402E", "actions": [{"executionTime": "1093ms", "package_id": "com.apple.TestFlight", "timestamp": 1745984288299, "type": "terminateApp", "action_id": "r34iB6SBag"}, {"executionTime": "643ms", "package_id": "au.com.kmart", "timestamp": 1745292775600, "type": "uninstallApp", "action_id": "xuUtNQjPFH"}, {"executionTime": "1157ms", "package_id": "com.apple.TestFlight", "timestamp": 1745292807279, "type": "launchApp", "action_id": "hxJ5UmgmCE"}, {"executionTime": "1583ms", "image_filename": "kmartau-install-se.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1745672162680, "type": "tap", "action_id": "8j6LdiS1RI"}, {"executionTime": "3307ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Install\"]", "method": "locator", "timeout": 10, "timestamp": 1745672202567, "type": "tap", "action_id": "LxL91EDe40"}, {"duration": 15, "executionTime": "5630ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Open\"]", "time": 15, "timeout": 60, "timestamp": 1745292879370, "type": "waitTill", "action_id": "cnVfNt021c"}, {"executionTime": "2245ms", "package_id": "au.com.kmart", "timestamp": 1745672556182, "type": "launchApp", "action_id": "1DvU5il4xI"}, {"executionTime": "1464ms", "image_filename": "onboarding-next.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1745292993676, "type": "tap", "x": 189, "y": 737, "action_id": "t6m63fjv64"}, {"executionTime": "1438ms", "image_filename": "start-testing-se.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1745293040067, "type": "tap", "x": 196, "y": 739, "action_id": "ZvNBpg7TkZ"}, {"executionTime": "1463ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746328442038, "type": "tap", "action_id": "j7peXr5yqO"}, {"executionTime": "2789ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnOnboardingScreenActionButton\"]", "method": "locator", "timeout": 10, "timestamp": 1745293083992, "type": "tap", "action_id": "BlHAbaH4w4"}, {"executionTime": "10879ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow\"]", "timeout": 10, "timestamp": 1745293134273, "type": "exists", "action_id": "5YbLHG7V0V"}, {"executionTime": "4195ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "method": "locator", "timeout": 10, "timestamp": 1745293198430, "type": "tap", "action_id": "4bqCE6flDg"}, {"executionTime": "1552ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746328514458, "type": "tap", "action_id": "Bk76MvsZ6W"}, {"executionTime": "363ms", "function_name": "alert_dismiss", "timestamp": 1746348969955, "type": "iosFunctions", "action_id": "zjhv18Ryar"}, {"executionTime": "1138ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnMayBeLater\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "action_id": "pIroVMGmPM"}, {"executionTime": "3558ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtOnePassOnboardingSkipForNow\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap", "action_id": "JCXabUEkPQ"}, {"duration": 15, "executionTime": "4568ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "time": 15, "timeout": 10, "timestamp": *************, "type": "waitTill", "action_id": "iLCW6tU9cA"}, {"type": "restartApp", "timestamp": *************, "package_id": "au.com.kmart", "action_id": "M7aqb4UWhq"}], "updated": "2025-05-04 20:09:16"}