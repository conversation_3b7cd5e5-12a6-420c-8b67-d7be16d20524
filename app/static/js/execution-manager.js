// Execution Manager for Mobile App Automation Tool - Refactored for Fetch API

/**
 * Handles the execution of test actions and test cases using Fetch API
 */
class ExecutionManager {
    constructor(appInstance) { // Pass the main app instance for access to fetchApi and actions
        this.app = appInstance; // Store reference to the main app
        this.isExecuting = false;
        this.currentIndex = -1;
        this.shouldStop = false;
        this.lastExecutionHadFailures = false; // Track if the last execution had failures
        this.globalValues = {}; // Store global values
        this.testCaseExecutionStates = []; // <-- ADDED: Internal state for test cases
        this._isAutoRerunning = false; // Flag to prevent nested auto-reruns

        // UI Elements
        this.executeBtn = document.getElementById('executeActions');
        this.stopBtn = document.getElementById('stopExecution');
        this.actionsListElement = document.getElementById('actionsList');

        // Create the execution overlay manager
        this.overlayManager = new ExecutionOverlayManager(this.app);

        // Create the fixed device screen manager
        this.fixedScreenManager = new FixedDeviceScreenManager(this.app);

        // Bind methods
        this.executeAllActions = this.executeAllActions.bind(this);
        this.stopExecution = this.stopExecution.bind(this);

        // Add initialization logic
        this.init();

        // Fetch global values
        this._fetchGlobalValues();
    }

    /**
     * Fetch global values from the server
     * @private
     */
    async _fetchGlobalValues() {
        try {
            const response = await this.app.fetchApi('settings', 'GET');
            if (response && response.global_values) {
                this.globalValues = response.global_values;
                console.log('Global values loaded:', this.globalValues);
            }
        } catch (error) {
            console.error('Error fetching global values:', error);
        }
    }

    /**
     * Get a global value by key
     * @param {string} key - The key of the global value
     * @param {*} defaultValue - The default value to return if the key is not found
     * @returns {*} The global value or the default value
     */
    getGlobalValue(key, defaultValue) {
        if (this.globalValues && key in this.globalValues) {
            return this.globalValues[key];
        }
        return defaultValue;
    }

    /**
     * Initialize the manager: add event listeners
     */
    init() {
        if (this.executeBtn) {
            this.executeBtn.addEventListener('click', this.executeAllActions);
            console.log("ExecutionManager: Event listener added to Execute All button.");
        } else {
            console.error("ExecutionManager: Execute All button not found!");
        }



        if (this.stopBtn) {
            this.stopBtn.addEventListener('click', this.stopExecution);
             console.log("ExecutionManager: Event listener added to Stop button.");
        } else {
            console.error("ExecutionManager: Stop button not found!");
        }

        // Add socket.io event listener for test retry
        if (typeof socket !== 'undefined' && socket) {
            socket.on('test_retry', this.handleTestRetry.bind(this));
            console.log("ExecutionManager: Added socket.io event listener for test retry");
        }

        // Add session health check listener
        this.healthCheckInterval = null;

        // Add socket.io event listener for session health status
        if (typeof socket !== 'undefined' && socket) {
            socket.on('session_health_status', this.handleSessionHealthStatus.bind(this));
            console.log("ExecutionManager: Added socket.io event listener for session health status");
        }
    }

    /**
     * Handle test retry event from the server
     * @param {Object} data - Retry data from the server
     */
    handleTestRetry(data) {
        console.log("ExecutionManager: Received test retry event", data);

        // Show a toast notification
        this.app.showToast(
            'Test Retry',
            `${data.message || 'Retrying test case...'}`,
            'warning'
        );

        // Log the retry with details
        this.app.logAction('warning', `${data.message || 'Retrying test case...'} (Attempt ${data.retry_count}/${data.max_retries})`);
        if (data.error) {
            this.app.logAction('error', `Previous attempt failed with error: ${data.error}`);
        }

        // Reset the last error in the app before retrying
        this.app.lastError = null;
        console.log("Reset app.lastError before test case retry");

        // Reset all action statuses
        this._clearAllActionStatuses();

        // Reset all test case headers
        const testCaseHeaders = this.actionsListElement.querySelectorAll('.test-case-header');
        testCaseHeaders.forEach(header => {
            header.classList.remove('success', 'error');
        });

        // Show retry progress in UI
        const progressElement = document.getElementById('retryProgress');
        if (progressElement) {
            progressElement.innerHTML = `<div class="retry-badge">Retry ${data.retry_count}/${data.max_retries}</div>`;
            progressElement.style.display = 'block';
        } else {
            // Create a retry progress element if it doesn't exist
            const progressContainer = document.createElement('div');
            progressContainer.id = 'retryProgress';
            progressContainer.className = 'retry-progress';
            progressContainer.innerHTML = `<div class="retry-badge">Retry ${data.retry_count}/${data.max_retries}</div>`;

            // Add it to the actions list container
            const actionsContainer = document.querySelector('.actions-container');
            if (actionsContainer) {
                actionsContainer.insertBefore(progressContainer, actionsContainer.firstChild);
            }
        }
    }

    /**
     * Handle session health status event from the server
     * @param {Object} data - Health status data from the server
     */
    handleSessionHealthStatus(data) {
        console.log("ExecutionManager: Received session health status", data);

        if (!data.healthy) {
            // Session is unhealthy, show warning and attempt recovery
            this.app.logAction('warning', `Appium session health check failed: ${data.message}`);

            // Show a toast notification
            this.app.showToast(
                'Session Health Warning',
                `${data.message || 'Appium session appears to be unresponsive'}`,
                'warning'
            );

            // Attempt recovery if we're still executing
            if (this.isExecuting) {
                this.app.logAction('info', 'Attempting to recover Appium session...');

                // Call the recovery API
                fetch('/api/device/recover', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        this.app.logAction('success', 'Successfully recovered Appium session');
                        this.app.showToast('Recovery Successful', 'Appium session recovered', 'success');
                    } else {
                        this.app.logAction('error', `Failed to recover Appium session: ${result.message}`);
                        this.app.showToast('Recovery Failed', result.message, 'error');

                        // If recovery failed and we're still executing, stop execution
                        if (this.isExecuting) {
                            this.app.logAction('error', 'Stopping execution due to unrecoverable session error');
                            this.stopExecution();
                        }
                    }
                })
                .catch(error => {
                    this.app.logAction('error', `Error during session recovery: ${error.message}`);

                    // If recovery failed and we're still executing, stop execution
                    if (this.isExecuting) {
                        this.app.logAction('error', 'Stopping execution due to unrecoverable session error');
                        this.stopExecution();
                    }
                });
            }
        } else {
            // Session is healthy, log if needed
            console.log("Appium session health check passed");
        }
    }

    /**
     * Start periodic health checks for the Appium session
     */
    startHealthChecks() {
        // Clear any existing interval
        this.stopHealthChecks();

        // Start a new interval (check every 30 seconds)
        this.healthCheckInterval = setInterval(() => {
            if (this.isExecuting) {
                console.log("Performing periodic Appium session health check");

                // Call the health check API
                fetch('/api/device/health_check')
                    .then(response => response.json())
                    .then(data => {
                        // The server will emit a socket event with the results
                        // which will be handled by handleSessionHealthStatus
                    })
                    .catch(error => {
                        console.error("Error during health check API call:", error);
                    });
            }
        }, 30000); // Check every 30 seconds

        console.log("Started periodic Appium session health checks");
    }

    /**
     * Stop periodic health checks
     */
    stopHealthChecks() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
            console.log("Stopped periodic Appium session health checks");
        }
    }

    /**
     * Execute all actions in the current action list using Fetch API
     */
    async executeAllActions() {
        console.log("ExecutionManager: executeAllActions called");
        if (this.isExecuting) {
            this.app.showToast('Error', 'Execution already in progress', 'error');
            return;
        }

        // Get actions directly from the app instance
        const currentActions = this.app.currentActions;

        console.log(`[ExecutionManager executeAllActions App Instance ID: ${this.app.instanceId}] In executeAllActions: this.app.currentActions is:`, currentActions, "Length:", currentActions ? currentActions.length : 'null or undefined'); // MODIFIED THIS LOG

        if (!currentActions || currentActions.length === 0) {
            this.app.showToast('Error', 'No actions to execute', 'error');
            return;
        }

        if (!this.app.isConnected) {
            this.app.showToast('Error', 'No device connected', 'error');
                return;
            }

        // Initialize report directory and screenshots folder only for test suites (multiple test cases)
        const testCaseContainers = this.actionsListElement.querySelectorAll('.test-case-container');
        const isTestSuite = testCaseContainers.length > 1;

        if (isTestSuite) {
            try {
                this.app.logAction('info', 'Initializing report directory and screenshots folder for test suite...');
                const reportResponse = await fetch('/api/report/initialize', {
                    method: 'POST'
                });

                const reportData = await reportResponse.json();

                if (reportResponse.ok) {
                    this.app.logAction('success', `${reportData.message}`);
                    this.app.logAction('info', `Report directory: ${reportData.report_dir}`);
                    this.app.logAction('info', `Screenshots directory: ${reportData.screenshots_dir}`);
                } else {
                    this.app.logAction('warning', `Failed to initialize report directory: ${reportData.message || 'Unknown error'}`);
                }
            } catch (error) {
                this.app.logAction('warning', `Error initializing report directory: ${error.message}`);
                console.error('Error initializing report directory:', error);
            }
        } else {
            this.app.logAction('info', 'Skipping report initialization - single test case execution');
        }

        // Delete all screenshots in app/static/screenshots folder
        try {
            this.app.logAction('info', 'Deleting all screenshots in app/static/screenshots folder...');
            const screenshotResponse = await fetch('/api/screenshots/delete_all', {
                method: 'POST'
            });

            const screenshotData = await screenshotResponse.json();

            if (screenshotResponse.ok) {
                this.app.logAction('success', `${screenshotData.message}`);
            } else {
                this.app.logAction('warning', `Failed to delete screenshots: ${screenshotData.message || 'Unknown error'}`);
            }
        } catch (error) {
            this.app.logAction('warning', `Error deleting screenshots: ${error.message}`);
            console.error('Error deleting screenshots:', error);
        }

        // Clear screenshots from the database before starting execution
        try {
            this.app.logAction('info', 'Clearing screenshots from database before execution...');
            const response = await fetch('/api/database/clear_screenshots', {
                method: 'POST'
            });

            const data = await response.json();

            if (response.ok) {
                this.app.logAction('success', `Cleared ${data.before_count} screenshots from database`);
            } else {
                this.app.logAction('warning', `Failed to clear screenshots: ${data.message || 'Unknown error'}`);
            }
        } catch (error) {
            this.app.logAction('warning', `Error clearing screenshots: ${error.message}`);
            console.error('Error clearing screenshots:', error);
        }

        // Initialize internal test case states
        this._initializeTestCaseStates();

        // Start execution
        this.isExecuting = true;
        this.shouldStop = false;
        this.currentIndex = -1; // Reset index

        // Clear the last error
        this.app.lastError = null;

        // Update UI for start
        this._updateUIForStart();

        // Clear previous statuses
        this._clearAllActionStatuses();

        // Show only the fixed device screen (no overlay)
        this.fixedScreenManager.fix();

        // Start periodic health checks
        this.startHealthChecks();

        this.app.logAction('info', `ExecutionManager: Starting execution of ${currentActions.length} actions...`);

        try {
            // Call the backend API to execute all actions
            // TODO: The backend /api/actions/execute_all needs to be implemented
            // It should iterate through actions, execute them, and report progress/results.
            // For now, we assume it executes all and returns a summary.

            // --- Placeholder for sequential execution ---
            // Since we don't have socket events, simulate sequential execution with fetch
            // This is less efficient than a dedicated endpoint or socket handling
            let allSucceeded = true;
            let currentlyHighlightedItem = null; // Track the currently highlighted DOM element

            // Keep track of current test case we're executing
            let currentTestCaseContainer = null;
            let currentInternalTestIdx = -1; // To track the testIdx for state updates

            // At the beginning of execution, reset test case headers but preserve already passed ones
            const testCaseHeaders = this.actionsListElement.querySelectorAll('.test-case-header');
            console.log(`Found ${testCaseHeaders.length} test case headers at execution start`);
            testCaseHeaders.forEach((header, index) => {
                const headerClasses = Array.from(header.classList);
                const hasSuccess = header.classList.contains('success');
                console.log(`Header ${index + 1}: classes=[${headerClasses.join(', ')}], hasSuccess=${hasSuccess}`);

                // Only reset headers that are not already marked as passed
                if (!hasSuccess) {
                    header.classList.remove('error');
                    console.log('Reset test case header (not passed):', header.textContent.trim());
                } else {
                    console.log('Preserving passed test case header:', header.textContent.trim());
                }
            });

            for (let i = 0; i < currentActions.length; i++) {

                // --- Stop Check ---
                if (this.shouldStop) {
                    this.app.logAction('warning', 'Execution stopped by user.');
                    allSucceeded = false;
                    // Remove any lingering highlight on stop
                    if (currentlyHighlightedItem) {
                        currentlyHighlightedItem.classList.remove('executing-highlight');
                    }
                    break;
                }
                // --- End Stop Check ---

                this.currentIndex = i;
                const action = currentActions[i];
                const actionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${i}"]`);

                // Check if this action is disabled
                if (action.enabled === false) {
                    this.app.logAction('info', `Skipping disabled action ${i + 1}/${currentActions.length}: ${this.app.actionManager.getActionDescription(action)}`);

                    // Update the action status to show it was skipped
                    this._updateActionStatusInUI(i, 'skipped', 'Action disabled');

                    // Update the fixed device screen status
                    if (this.fixedScreenManager) {
                        const actionDescription = this.app.actionManager.getActionDescription(action);
                        this.fixedScreenManager.updateStatus(`⊘ ${actionDescription} - Skipped (disabled)`, i, currentActions.length);
                    }

                    continue; // Skip to next action
                }

                // --- De-highlight Previous Action ---
                if (currentlyHighlightedItem) {
                    currentlyHighlightedItem.classList.remove('executing-highlight');
                    currentlyHighlightedItem = null;
                }
                // --- End De-highlight ---

                this.app.logAction('info', `Executing action ${i + 1}/${currentActions.length}: ${this.app.actionManager.getActionDescription(action)}`);

                // Log action status with action ID if available
                if (action.action_id) {
                    this.app.logActionStatus(action.action_id, 'running');
                }

                // Check if this action belongs to a different test case than the previous one
                if (actionItem) {
                    const testCaseContainer = actionItem.closest('.test-case-container');

                    // If we've moved to a new test case, expand it and make it visible
                    if (testCaseContainer && testCaseContainer !== currentTestCaseContainer) {
                        if (currentTestCaseContainer) {
                            // Finalize status of the previous test case if it was still running
                            if (currentInternalTestIdx !== -1) {
                                const prevState = this.testCaseExecutionStates.find(s => s.testIdx === currentInternalTestIdx);
                                if (prevState && prevState.status === 'running') {
                                    this._updateTestCaseState(currentInternalTestIdx, 'passed');
                                }
                            }
                            // Remove current-test-case class from previous test case
                            currentTestCaseContainer.classList.remove('current-test-case');
                            // Don't collapse previous test case yet - we'll do that at the end
                        }

                        // Check if this test case is already passed and should be skipped
                        const testCaseHeader = testCaseContainer.querySelector('.test-case-header');
                        const isAlreadyPassed = testCaseHeader && testCaseHeader.classList.contains('success');

                        // Debug logging
                        console.log(`Checking test case: ${testCaseContainer.dataset.testCaseName || 'Unknown'}`);
                        console.log(`Test case header classes:`, testCaseHeader ? Array.from(testCaseHeader.classList) : 'No header found');
                        console.log(`Is already passed:`, isAlreadyPassed);

                        if (isAlreadyPassed) {
                            this.app.logAction('info', `Skipping test case - already passed: ${testCaseContainer.dataset.testCaseName || 'Unknown'}`);

                            // Skip all actions in this test case
                            let nextTestCaseIndex = i;
                            for (let j = i; j < currentActions.length; j++) {
                                const nextActionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${j}"]`);
                                const nextActionTestCase = nextActionItem ? nextActionItem.closest('.test-case-container') : null;

                                if (nextActionTestCase === testCaseContainer) {
                                    // This action belongs to the current (passed) test case, skip it
                                    this._updateActionStatusInUI(j, 'skipped', 'Test case already passed');
                                    nextTestCaseIndex = j;
                                } else {
                                    // We've reached the next test case, break
                                    break;
                                }
                            }

                            // Jump to the next test case
                            i = nextTestCaseIndex;
                            continue;
                        }

                        // Store the new current test case
                        currentTestCaseContainer = testCaseContainer;

                        // Mark this as the current test case
                        testCaseContainer.classList.add('current-test-case');
                        const testCaseId = testCaseContainer.dataset.testCaseId || testCaseContainer.dataset.testCaseName || 'unknown';
                        const testIdx = parseInt(testCaseContainer.dataset.testIdx || '-1'); // Ensure it's a number
                        console.log(`Marked test case ${testCaseId} as current with test_idx=${testIdx}`);
                        currentInternalTestIdx = testIdx; // Update current internal testIdx

                        // Update internal state to 'running'
                        if (currentInternalTestIdx !== -1) {
                            this._updateTestCaseState(currentInternalTestIdx, 'running');
                        }

                        // Store the test_idx for later use with actions
                        console.log(`Will use test_idx=${testIdx} for actions in this test case`);

                        // Make sure the test case is expanded
                        const actionsContainer = testCaseContainer.querySelector('.test-case-actions');
                        if (actionsContainer && !actionsContainer.classList.contains('show')) {
                            // Get the header that controls this container
                            const header = testCaseContainer.querySelector('.test-case-header');
                            if (header) {
                                header.click(); // Programmatically click to expand
                            }
                        }
                    }
                }

                // 1. Update status (spinner)
                this._updateActionStatusInUI(i, 'running');

                // Update the fixed device screen status
                if (this.fixedScreenManager) {
                    const actionDescription = this.app.actionManager.getActionDescription(action);
                    this.fixedScreenManager.updateStatus(actionDescription, i, currentActions.length);
                }

                // 2. Highlight current item and scroll to it
                if (actionItem) {
                    actionItem.classList.add('executing-highlight');
                    // Scroll to the item so we can see which step is running
                    actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    currentlyHighlightedItem = actionItem; // Track the newly highlighted item

                    // Make sure the action item is visible by applying the scrollIntoView again
                    setTimeout(() => {
                        actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }, 100);
                }

                try {
                    // Special handling for Multi Step action type
                    if (action.type === 'multiStep') {


                        // Load the test case steps
                        try {
                            this.app.logAction('info', `Loading steps for Multi Step action: ${action.test_case_name}`);

                            // Load the test case steps using the MultiStepAction module
                            const testCaseSteps = await this.app.multiStepAction.loadTestCaseSteps(action.test_case_id);

                            if (!testCaseSteps || testCaseSteps.length === 0) {
                                throw new Error('No steps found in the test case');
                            }

                            this.app.logAction('info', `Loaded ${testCaseSteps.length} steps from test case: ${action.test_case_name}`);

                            // Create a container for the multi-step action if it doesn't exist
                            let multiStepContainer = actionItem.querySelector('.multi-step-container');
                            if (!multiStepContainer) {
                                multiStepContainer = document.createElement('div');
                                multiStepContainer.className = 'multi-step-container mt-2 ps-4 border-start';
                                actionItem.appendChild(multiStepContainer);
                            }

                            // Clear any existing steps
                            multiStepContainer.innerHTML = '';

                            // Create step items for each step in the test case
                            for (let j = 0; j < testCaseSteps.length; j++) {
                                const step = testCaseSteps[j];
                                const stepItem = document.createElement('div');
                                stepItem.className = 'multi-step-item d-flex justify-content-between align-items-center mb-1';
                                stepItem.dataset.stepIndex = j;

                                // Create step content
                                const stepContent = document.createElement('div');
                                stepContent.className = 'multi-step-content';
                                stepContent.innerHTML = `
                                    <span class="badge bg-secondary me-2">${j + 1}</span>
                                    <span class="badge bg-primary me-2">${step.type}</span>
                                    ${this.app.actionManager.getActionDescription(step)}
                                `;

                                // Create step status
                                const stepStatus = document.createElement('div');
                                stepStatus.className = 'multi-step-status';

                                // Add to step item
                                stepItem.appendChild(stepContent);
                                stepItem.appendChild(stepStatus);

                                // Add to container
                                multiStepContainer.appendChild(stepItem);
                            }

                            // Show the multi-step container
                            multiStepContainer.style.display = 'block';

                            // Execute each step in the test case
                            let multiStepSuccess = true;

                            for (let j = 0; j < testCaseSteps.length; j++) {
                                // Check if execution should stop
                                if (this.shouldStop) {
                                    this.app.logAction('warning', 'Multi Step execution stopped by user.');
                                    multiStepSuccess = false;
                                    break;
                                }

                                const step = testCaseSteps[j];
                                const stepItem = multiStepContainer.querySelector(`.multi-step-item[data-step-index="${j}"]`);

                                // Update step status to running
                                if (stepItem) {
                                    const stepStatus = stepItem.querySelector('.multi-step-status');
                                    if (stepStatus) {
                                        stepStatus.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
                                    }

                                    // Highlight the current step
                                    stepItem.classList.add('multi-step-executing');

                                    // Scroll to the step
                                    stepItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

                                    // Make sure the step item is visible by applying the scrollIntoView again
                                    setTimeout(() => {
                                        stepItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                                    }, 100);
                                }

                                // Execute the step
                                this.app.logAction('info', `Executing Multi Step action step ${j + 1}/${testCaseSteps.length}: ${this.app.actionManager.getActionDescription(step)}`);

                                try {
                                    // Check if this is a hook action - these should be skipped during normal execution
                                    if (step.action_type === 'hookAction') {
                                        this.app.logAction('info', `Skipping Hook Action step ${j + 1} during normal execution. This action will only be executed when a step fails.`);

                                        // Update step status to success (skipped)
                                        if (stepItem) {
                                            const stepStatus = stepItem.querySelector('.multi-step-status');
                                            if (stepStatus) {
                                                stepStatus.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                                            }
                                            stepItem.classList.add('multi-step-success');

                                            // Add tooltip explaining it's skipped
                                            stepItem.setAttribute('title', 'Hook Action skipped (will only be executed when a step fails)');
                                            stepItem.setAttribute('data-bs-toggle', 'tooltip');
                                            stepItem.setAttribute('data-bs-placement', 'top');
                                        }

                                        // Continue to next step
                                        continue;
                                    }

                                    // Add test_idx to the step if it's not already set
                                    if (currentTestCaseContainer && !step.test_idx) {
                                        const testIdx = currentTestCaseContainer.dataset.testIdx || '0';

                                        // Add a delay before executing multi-step action to ensure test_idx is correctly handled
                                        if (j === 0) { // Only add delay for the first step in a multi-step action
                                            const stepDelay = this.getGlobalValue('step_delay', 2000); // Default to 2 seconds if not set
                                            console.log(`Adding a ${stepDelay/1000}-second delay before executing multi-step action for test_idx=${testIdx}`);
                                            await new Promise(resolve => setTimeout(resolve, stepDelay));
                                            console.log(`Delay completed, continuing with multi-step action for test_idx=${testIdx}`);
                                        }

                                        step.test_idx = parseInt(testIdx);
                                        console.log(`Added test_idx=${testIdx} to multi-step action data for execution`);
                                    }
                                    // Update parent test case to running if not already
                                    if (currentInternalTestIdx !== -1) {
                                        const parentTcState = this.testCaseExecutionStates.find(s => s.testIdx === currentInternalTestIdx);
                                        if (parentTcState && parentTcState.status !== 'running' && parentTcState.status !== 'failed') {
                                            this._updateTestCaseState(currentInternalTestIdx, 'running');
                                        }
                                    }

                                    const stepResult = await this.app.fetchApi('action/execute', 'POST', {
                                        action: step,
                                        force_screenshot: true
                                    });

                                    // Update step status based on result
                                    if (stepItem) {
                                        const stepStatus = stepItem.querySelector('.multi-step-status');
                                        if (stepStatus) {
                                            if (stepResult.success) {
                                                stepStatus.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                                                stepItem.classList.add('multi-step-success');
                                            } else {
                                                stepStatus.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                                                stepItem.classList.add('multi-step-error');

                                                // Add tooltip with error message
                                                if (stepResult.error) {
                                                    stepItem.setAttribute('title', stepResult.error);
                                                    stepItem.setAttribute('data-bs-toggle', 'tooltip');
                                                    stepItem.setAttribute('data-bs-placement', 'top');
                                                }

                                                // Make sure to remove the executing highlight
                                                stepItem.classList.remove('multi-step-executing');

                                                multiStepSuccess = false;
                                                this.app.logAction('error', `Multi Step action step ${j + 1} failed: ${stepResult.error}`);

                                                // Set the last error
                                                this.app.lastError = stepResult.error;

                                                // Update the parent multi-step action to show error status immediately
                                                this._updateActionStatusInUI(i, 'error', 'One or more steps failed');
                                                // Mark the internal test case state as failed
                                                if (currentInternalTestIdx !== -1) {
                                                    this._updateTestCaseState(currentInternalTestIdx, 'failed', stepResult.error);
                                                }

                                                // Check if there are any hook actions in the test case steps
                                                const multiStepHookActions = testCaseSteps.filter(s => s.action_type === 'hookAction');

                                                if (multiStepHookActions && multiStepHookActions.length > 0) {
                                                    this.app.logAction('info', `Found ${multiStepHookActions.length} hook actions within multi-step. The backend will execute them and retry the failed step.`);

                                                    // The backend will handle executing the hook actions and retrying the failed step
                                                    // We just need to wait for the result

                                                    // Don't break here, let the backend handle the retry
                                                    continue;
                                                }

                                                break; // Stop on first error if no hook actions
                                            }
                                        }

                                        // Remove executing highlight
                                        stepItem.classList.remove('multi-step-executing');
                                    }

                                    // Refresh screenshot
                                    await this.app.refreshScreenshot();
                                    await new Promise(resolve => setTimeout(resolve, 500));

                                } catch (stepError) {
                                    // Update step status to error
                                    if (stepItem) {
                                        const stepStatus = stepItem.querySelector('.multi-step-status');
                                        if (stepStatus) {
                                            stepStatus.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                                        }

                                        // Add tooltip with error message
                                        stepItem.setAttribute('title', stepError.message);
                                        stepItem.setAttribute('data-bs-toggle', 'tooltip');
                                        stepItem.setAttribute('data-bs-placement', 'top');

                                        // Remove executing highlight
                                        stepItem.classList.remove('multi-step-executing');
                                        stepItem.classList.add('multi-step-error');
                                    }

                                    multiStepSuccess = false;
                                    this.app.logAction('error', `Error executing Multi Step action step ${j + 1}: ${stepError.message}`);

                                    // Set the last error
                                    this.app.lastError = stepError.message;

                                    // Update the parent multi-step action to show error status immediately
                                    this._updateActionStatusInUI(i, 'error', 'One or more steps failed');
                                    // Mark the internal test case state as failed
                                    if (currentInternalTestIdx !== -1) {
                                        this._updateTestCaseState(currentInternalTestIdx, 'failed', stepError.message);
                                    }

                                    // Check if there are any hook actions in the test case steps
                                    const multiStepHookActions = testCaseSteps.filter(s => s.action_type === 'hookAction');

                                    if (multiStepHookActions && multiStepHookActions.length > 0) {
                                        this.app.logAction('info', `Found ${multiStepHookActions.length} hook actions within multi-step. The backend will execute them and retry the failed step.`);

                                        // The backend will handle executing the hook actions and retrying the failed step
                                        // We just need to wait for the result

                                        // Don't break here, let the backend handle the retry
                                        continue;
                                    }

                                    break; // Stop on error if no hook actions
                                }
                            }

                            // Check if this is a new test case
                            const isNewTestCase = true; // Assume each multi-step action is its own test case

                            // Break out of current test case on failure (server will handle retry)
                            if (!multiStepSuccess && isNewTestCase) {
                                // Mark the current test case as failed and move to the next test case
                                this.app.logAction('info', 'Moving to the next test case after failure (server will handle retry)');

                                // Skip to the next test case using the helper function
                                i = this._handleTestCaseFailure(i, currentTestCaseContainer);

                                continue;
                            }

                            // Update the main action status based on the multi-step execution result
                            if (multiStepSuccess) {
                                this._updateActionStatusInUI(i, 'success', `Successfully executed all ${testCaseSteps.length} steps`);
                                await this.app.refreshScreenshot();
                            } else {
                                // Make sure the error status is applied to the parent multi-step action
                                this._updateActionStatusInUI(i, 'error', 'One or more steps failed');

                                // Make sure the action item has the error class
                                const actionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${i}"]`);
                                if (actionItem) {
                                    actionItem.classList.remove('success');
                                    actionItem.classList.add('error', 'action-item-failed');

                                    // Make sure any spinner is removed
                                    const spinner = actionItem.querySelector('.spinner-border');
                                    if (spinner) {
                                        const statusElement = spinner.closest('.action-status');
                                        if (statusElement) {
                                            statusElement.innerHTML = '<i class="bi bi-x-circle"></i>';
                                        }
                                    }

                                    // Update the test case header to show error status
                                    const testCaseContainer = actionItem.closest('.test-case-container');
                                    if (testCaseContainer) {
                                        const header = testCaseContainer.querySelector('.test-case-header');
                                        if (header) {
                                            header.classList.remove('success');
                                            header.classList.add('error');
                                        }
                                    }
                                }

                                allSucceeded = false;

                                // Continue to the next test case instead of stopping
                                continue;
                            }

                        } catch (loadError) {
                            this._updateActionStatusInUI(i, 'error', `Failed to load test case steps: ${loadError.message}`);
                            this.app.logAction('error', `Failed to load test case steps for Multi Step action: ${loadError.message}`);
                            allSucceeded = false;

                            // Continue to the next test case instead of stopping
                            continue;
                        }
                    } else {
                        // Check if this is a Hook Action - these should only be executed when a step fails
                        if (action.type === 'hookAction') {
                            this.app.logAction('info', `Skipping Hook Action during normal execution. This action will only be executed when a step fails.`);
                            this._updateActionStatusInUI(i, 'success', 'Hook Action skipped (will only be executed when a step fails)');
                            await this.app.refreshScreenshot();
                            await new Promise(resolve => setTimeout(resolve, 500));
                        } else {
                            // Normal action execution
                            // Add test_idx to the action if it's not already set
                            if (currentTestCaseContainer && !action.test_idx) {
                                const testIdx = currentTestCaseContainer.dataset.testIdx || '0';

                                // Check if this is a new test case by comparing with the previous action's test case
                                if (i > 0) {
                                    // Get the previous action's test case container
                                    const prevActionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${i-1}"]`);
                                    if (prevActionItem) {
                                        const prevTestCaseContainer = prevActionItem.closest('.test-case-container');
                                        if (prevTestCaseContainer && prevTestCaseContainer !== currentTestCaseContainer) {
                                            // This is the first action of a new test case
                                            console.log(`Detected new test case at index ${i}. Previous test case: ${prevTestCaseContainer.dataset.testCaseId}, Current test case: ${currentTestCaseContainer.dataset.testCaseId}`);

                                            // Get the test case delay from global values (default to 10 seconds if not set)
                                            const testCaseDelay = this.getGlobalValue('test_case_delay', 10000);
                                            console.log(`Adding a ${testCaseDelay/1000}-second delay before starting a new test case at index ${i}`);

                                            // Add a delay between test cases
                                            await new Promise(resolve => setTimeout(resolve, testCaseDelay));

                                            console.log(`Delay completed, continuing with test case at index ${i}`);
                                        }
                                    }
                                }

                                action.test_idx = parseInt(testIdx);
                                console.log(`Added test_idx=${testIdx} to action data for execution`);
                            }

                            const result = await this.app.fetchApi('action/execute', 'POST', {
                                action: action,
                                force_screenshot: true
                            });

                            // 4. Process Result (Update status icon)
                            if (result.success) {
                                // Store the duration if returned by the server
                                if (result.duration) {
                                    action.executionTime = result.duration;
                                }

                                this._updateActionStatusInUI(i, 'success', result.message);

                                // Log action status with action ID if available
                                if (action.action_id) {
                                    this.app.logActionStatus(action.action_id, 'pass');
                                }

                                // Update fixed device screen status with success message
                                if (this.fixedScreenManager) {
                                    const actionDescription = this.app.actionManager.getActionDescription(action);
                                    this.fixedScreenManager.updateStatus(`✓ ${actionDescription}`, i, currentActions.length);
                                }

                                await this.app.refreshScreenshot();
                                await new Promise(resolve => setTimeout(resolve, 500));
                            } else {
                                this._updateActionStatusInUI(i, 'error', result.error);

                                // Update fixed device screen status with error message
                                if (this.fixedScreenManager) {
                                    const actionDescription = this.app.actionManager.getActionDescription(action);
                                    this.fixedScreenManager.updateStatus(`✗ ${actionDescription} - Failed`, i, currentActions.length);
                                }

                                this.app.logAction('error', `Action ${i + 1} failed: ${result.error}`);

                                // Log action status with action ID if available
                                if (action.action_id) {
                                    this.app.logActionStatus(action.action_id, 'fail');
                                }

                                // Set the last error
                                this.app.lastError = result.error;

                                // Check if there are Hook Actions in the actions list
                                const hookActions = this._findHookActions(currentActions);

                                if (hookActions && hookActions.length > 0) {
                                    // Found Hook Actions, execute them in sequence
                                    this.app.logAction('info', `Action failed. Executing ${hookActions.length} Hook Actions for recovery...`);

                                    // Update UI to show recovery in progress
                                    this._updateActionStatusInUI(i, 'recovery', `Executing ${hookActions.length} recovery actions...`);

                                    // Find all hook action items and highlight them
                                    const hookActionItems = [];
                                    for (const hookAction of hookActions) {
                                        const hookActionIndex = currentActions.indexOf(hookAction);
                                        if (hookActionIndex >= 0) {
                                            const hookActionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${hookActionIndex}"]`);
                                            if (hookActionItem) {
                                                hookActionItems.push({
                                                    index: hookActionIndex,
                                                    element: hookActionItem,
                                                    action: hookAction
                                                });
                                            }
                                        }
                                    }

                                    // Add a spinner to the first hook action
                                    if (hookActionItems.length > 0) {
                                        const firstHookItem = hookActionItems[0].element;
                                        firstHookItem.classList.add('executing-highlight', 'hook-executing');

                                        const hookStatusContainer = firstHookItem.querySelector('.action-status');
                                        if (hookStatusContainer) {
                                            hookStatusContainer.innerHTML = '<span class="spinner-border spinner-border-sm text-warning" role="status"></span>';
                                        }
                                    }

                                    try {
                                        // Execute all Hook Actions in sequence
                                        this.app.logAction('info', `Executing ${hookActions.length} Hook Actions via dedicated handler...`);
                                        // Get the current test case ID if available
                                        let testCaseId = '';
                                        if (currentTestCaseContainer) {
                                            testCaseId = currentTestCaseContainer.dataset.testCaseId || '';
                                        }

                                        // Add test_idx to the hook actions if it's not already set
                                        if (currentTestCaseContainer) {
                                            const testIdx = currentTestCaseContainer.dataset.testIdx || '0';

                                            // Add a delay before executing hook actions to ensure test_idx is correctly handled
                                            const hookDelay = this.getGlobalValue('hook_delay', 2000); // Default to 2 seconds if not set
                                            console.log(`Adding a ${hookDelay/1000}-second delay before executing hook actions for test_idx=${testIdx}`);
                                            await new Promise(resolve => setTimeout(resolve, hookDelay));
                                            console.log(`Delay completed, continuing with hook actions for test_idx=${testIdx}`);

                                            // Add test_idx to each hook action
                                            for (const hookAction of hookActions) {
                                                if (!hookAction.test_idx) {
                                                    hookAction.test_idx = parseInt(testIdx);
                                                    console.log(`Added test_idx=${testIdx} to hook action data for execution`);
                                                }
                                            }
                                            // Also add test_idx to the failed action if it's not already set
                                            if (!action.test_idx) {
                                                action.test_idx = parseInt(testIdx);
                                                console.log(`Added test_idx=${testIdx} to failed action data for hook execution`);
                                            }
                                        }

                                        const hookResult = await this.app.fetchApi('action/execute_hook', 'POST', {
                                            actions: hookActions,
                                            failed_action: action,
                                            failed_action_index: i,
                                            force_screenshot: true,
                                            test_case_id: testCaseId
                                        });

                                        if (hookResult.success) {
                                            // Log the hook actions summary
                                            if (hookResult.hook_actions_summary && hookResult.hook_actions_summary.length > 0) {
                                                for (const summary of hookResult.hook_actions_summary) {
                                                    const status = summary.success ? 'success' : 'error';
                                                    const icon = summary.success ? '✅' : '❌';
                                                    this.app.logAction(status, `Hook Action ${summary.index}: ${summary.type} - ${icon} ${summary.message}`);
                                                }
                                            } else {
                                                this.app.logAction('success', `All hook actions executed successfully`);
                                            }

                                            // Update all hook action items in the UI
                                            for (let j = 0; j < hookActionItems.length; j++) {
                                                const hookActionItem = hookActionItems[j];
                                                const hookAction = hookActionItem.action;
                                                const element = hookActionItem.element;

                                                // Get the status for this hook action from the summary
                                                let success = true;
                                                let message = '';

                                                if (hookResult.hook_actions_summary && hookResult.hook_actions_summary.length > j) {
                                                    const summary = hookResult.hook_actions_summary[j];
                                                    success = summary.success;
                                                    message = summary.message;
                                                }

                                                // Update the hook action item in the UI
                                                if (element) {
                                                    // Remove executing highlight
                                                    element.classList.remove('executing-highlight', 'hook-executing');

                                                    // Update the status icon
                                                    const statusContainer = element.querySelector('.action-status');
                                                    if (statusContainer) {
                                                        if (success) {
                                                            statusContainer.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                                                        } else {
                                                            statusContainer.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                                                        }
                                                    }

                                                    // Get a description of what the hook action did
                                                    let hookActionDescription = '';
                                                    if (hookAction.hook_type === 'tap') {
                                                        if (hookAction.hook_data.method === 'image' && hookAction.hook_data.image_filename) {
                                                            hookActionDescription = `tap on image '${hookAction.hook_data.image_filename}'`;
                                                        } else if (hookAction.hook_data.method === 'locator' && hookAction.hook_data.locator_type && hookAction.hook_data.locator_value) {
                                                            hookActionDescription = `tap on ${hookAction.hook_data.locator_type}: '${hookAction.hook_data.locator_value}'`;
                                                        } else if (hookAction.hook_data.method === 'coordinates' && hookAction.hook_data.x !== undefined && hookAction.hook_data.y !== undefined) {
                                                            hookActionDescription = `tap at coordinates (${hookAction.hook_data.x}, ${hookAction.hook_data.y})`;
                                                        } else if (hookAction.hook_data.method === 'text' && hookAction.hook_data.text) {
                                                            hookActionDescription = `tap on text '${hookAction.hook_data.text}'`;
                                                        }
                                                    } else if (hookAction.hook_type === 'swipe') {
                                                        hookActionDescription = `swipe from (${hookAction.hook_data.start_x}, ${hookAction.hook_data.start_y}) to (${hookAction.hook_data.end_x}, ${hookAction.hook_data.end_y})`;
                                                    } else if (hookAction.hook_type === 'text') {
                                                        hookActionDescription = `input text '${hookAction.hook_data.text}'`;
                                                    } else if (hookAction.hook_type === 'wait') {
                                                        hookActionDescription = `wait for ${hookAction.hook_data.duration} seconds`;
                                                    } else if (hookAction.hook_type === 'iosFunctions') {
                                                        hookActionDescription = `iOS function '${hookAction.hook_data.function_name}'`;
                                                    }

                                                    // Add tooltip with detailed information
                                                    if (hookActionDescription) {
                                                        element.setAttribute('title', `${hookAction.hook_type} - ${hookActionDescription} - ${message}`);
                                                        element.setAttribute('data-bs-toggle', 'tooltip');
                                                        element.setAttribute('data-bs-placement', 'top');
                                                    }

                                                    // Update the action item text to show the hook details
                                                    const actionContent = element.querySelector('.action-content');
                                                    if (actionContent) {
                                                        // Find the text that says "Hook Action: tap (Recovery)" and update it
                                                        const hookActionText = actionContent.querySelector('.action-text');
                                                        if (hookActionText) {
                                                            hookActionText.textContent = `Hook Action ${j+1}: ${hookAction.hook_type} (${hookActionDescription})`;
                                                        }

                                                        // Find the badge that says "Recovery Action" and add the details after it
                                                        const recoveryBadge = actionContent.querySelector('.badge.bg-warning');
                                                        if (recoveryBadge) {
                                                            // Create a new span with the hook details
                                                            const detailsSpan = document.createElement('span');
                                                            detailsSpan.className = 'ms-2 text-muted';
                                                            detailsSpan.textContent = `(${hookActionDescription})`;

                                                            // Check if a details span already exists and remove it
                                                            const existingDetailsSpan = recoveryBadge.nextElementSibling;
                                                            if (existingDetailsSpan && existingDetailsSpan.classList.contains('text-muted')) {
                                                                existingDetailsSpan.remove();
                                                            }

                                                            // Insert the details span after the recovery badge
                                                            recoveryBadge.insertAdjacentElement('afterend', detailsSpan);
                                                        }
                                                    }
                                                }
                                            }

                                            // Update the failed action status to show retrying
                                            this._updateActionStatusInUI(i, 'retrying', `Retrying action after recovery...`);

                                            // Retry the failed action
                                            this.app.logAction('info', `Retrying failed action...`);
                                            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry

                                            // Add test_idx to the action if it's not already set
                                            if (currentTestCaseContainer && !action.test_idx) {
                                                const testIdx = currentTestCaseContainer.dataset.testIdx || '0';

                                                // Add a delay before retrying the action to ensure test_idx is correctly handled
                                                const retryDelay = this.getGlobalValue('retry_delay', 2000); // Default to 2 seconds if not set
                                                console.log(`Adding a ${retryDelay/1000}-second delay before retrying action for test_idx=${testIdx}`);
                                                await new Promise(resolve => setTimeout(resolve, retryDelay));
                                                console.log(`Delay completed, continuing with action retry for test_idx=${testIdx}`);

                                                action.test_idx = parseInt(testIdx);
                                                console.log(`Added test_idx=${testIdx} to action data for retry execution`);
                                            }

                                            const retryResult = await this.app.fetchApi('action/execute', 'POST', {
                                                action: action,
                                                force_screenshot: true
                                            });

                                            if (retryResult.success) {
                                                // Retry succeeded - explicitly remove error class from the action item
                                                const actionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${i}"]`);
                                                if (actionItem) {
                                                    // First remove all status classes
                                                    actionItem.classList.remove('error', 'action-item-failed', 'action-item-recovery', 'action-item-retrying');
                                                    // Then add success class
                                                    actionItem.classList.add('success');
                                                }

                                                // Update UI with success status
                                                this._updateActionStatusInUI(i, 'success', `Action succeeded after recovery`);

                                                // Update test case header status to success if this was the only failing action
                                                if (currentTestCaseContainer) {
                                                    const header = currentTestCaseContainer.querySelector('.test-case-header');
                                                    if (header) {
                                                        // Check if there are any other error actions in this test case
                                                        const otherErrorActions = currentTestCaseContainer.querySelectorAll('.action-item.error');
                                                        if (otherErrorActions.length === 0) {
                                                            // No other errors, so mark the test case as success
                                                            header.classList.remove('error');
                                                            header.classList.add('success');
                                                        }
                                                    }
                                                }

                                                await this.app.refreshScreenshot();
                                                await new Promise(resolve => setTimeout(resolve, 500));
                                                continue; // Continue with the next action
                                            } else {
                                                // Retry failed
                                                this._updateActionStatusInUI(i, 'error', `Action failed even after recovery: ${retryResult.error}`);
                                                this.app.logAction('error', `Action ${i + 1} failed even after recovery: ${retryResult.error}`);
                                                allSucceeded = false;

                                                // Check if we should continue on error
                                                const continueOnError = true; // Always continue to next test case
                                                if (!continueOnError) {
                                                    break; // Stop on error
                                                }

                                                // Continue with next action
                                                continue;
                                            }
                                        } else {
                                            // All Hook Actions failed
                                            this.app.logAction('error', `All Hook Actions failed: ${hookResult.error}`);

                                            // Log the hook actions summary if available
                                            if (hookResult.hook_actions_summary && hookResult.hook_actions_summary.length > 0) {
                                                for (const summary of hookResult.hook_actions_summary) {
                                                    this.app.logAction('error', `Hook Action ${summary.index}: ${summary.type} - ❌ ${summary.message}`);
                                                }
                                            }

                                            // Update all hook action items in the UI
                                            for (let j = 0; j < hookActionItems.length; j++) {
                                                const hookActionItem = hookActionItems[j];
                                                const element = hookActionItem.element;

                                                // Get the status for this hook action from the summary
                                                let message = '';

                                                if (hookResult.hook_actions_summary && hookResult.hook_actions_summary.length > j) {
                                                    const summary = hookResult.hook_actions_summary[j];
                                                    message = summary.message;
                                                }

                                                // Update the hook action item in the UI
                                                if (element) {
                                                    // Remove executing highlight
                                                    element.classList.remove('executing-highlight', 'hook-executing');

                                                    // Update the status icon
                                                    const statusContainer = element.querySelector('.action-status');
                                                    if (statusContainer) {
                                                        statusContainer.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                                                    }

                                                    // Add tooltip with error message
                                                    if (message) {
                                                        element.setAttribute('title', `Failed: ${message}`);
                                                        element.setAttribute('data-bs-toggle', 'tooltip');
                                                        element.setAttribute('data-bs-placement', 'top');
                                                    }
                                                }
                                            }

                                            this._updateActionStatusInUI(i, 'error', `Action failed and all recovery actions failed: ${result.error}`);
                                            allSucceeded = false;

                                            // Check if we should continue on error
                                            const continueOnError = true; // Always continue to next test case
                                            if (!continueOnError) {
                                                break; // Stop on error
                                            }

                                            // Continue with next action
                                            continue;
                                        }
                                    } catch (hookError) {
                                        // Error executing Hook Actions
                                        this.app.logAction('error', `Error executing Hook Actions: ${hookError.message}`);

                                        // Update all hook action items in the UI
                                        for (let j = 0; j < hookActionItems.length; j++) {
                                            const hookActionItem = hookActionItems[j];
                                            const element = hookActionItem.element;

                                            // Update the hook action item in the UI
                                            if (element) {
                                                // Remove executing highlight
                                                element.classList.remove('executing-highlight', 'hook-executing');

                                                // Update the status icon
                                                const statusContainer = element.querySelector('.action-status');
                                                if (statusContainer) {
                                                    statusContainer.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                                                }

                                                // Add tooltip with error message
                                                element.setAttribute('title', `Error: ${hookError.message}`);
                                                element.setAttribute('data-bs-toggle', 'tooltip');
                                                element.setAttribute('data-bs-placement', 'top');
                                            }
                                        }

                                        this._updateActionStatusInUI(i, 'error', `Action failed and recovery failed: ${result.error}`);
                                        allSucceeded = false;

                                        // Check if we should continue on error
                                        const continueOnError = true; // Always continue to next test case
                                        if (!continueOnError) {
                                            break; // Stop on error
                                        }

                                        // Continue with next action
                                        continue;
                                    }
                                } else {
                                    // No Hook Action found, just mark as failed but continue to next test case
                                    allSucceeded = false;

                                    // Check if we should continue on error
                                    const continueOnError = true; // Always continue to next test case
                                    if (!continueOnError) {
                                        break; // Stop on error
                                    }

                                    // Skip to the next test case using the helper function
                                    i = this._handleTestCaseFailure(i, currentTestCaseContainer);

                                    // Continue with next action (next test case)
                                    continue;
                                }
                            }
                        }
                    }
                } catch (actionError) {
                    this._updateActionStatusInUI(i, 'error', actionError.message);
                    this.app.logAction('error', `Error executing action ${i + 1}: ${actionError.message}`);
                    allSucceeded = false;

                    // ***** ADD THIS BLOCK to update internal TC state *****
                    if (currentInternalTestIdx !== -1) {
                        console.log(`Action ${i + 1} (in TC ${currentInternalTestIdx}) failed. Updating TC state to 'failed'. Error: ${actionError.message}`);
                        this._updateTestCaseState(currentInternalTestIdx, 'failed', actionError.message);
                    } else {
                        console.warn(`Action ${i + 1} failed, but currentInternalTestIdx is -1. Cannot set TC status to failed for this action's test case.`);
                    }
                    // ***** END ADDED BLOCK *****

                    // Check if we should continue on error
                    const continueOnError = true; // Always continue to next test case
                    if (!continueOnError) {
                        break; // Stop on error
                    }

                    // Skip to the next test case using the helper function
                    i = this._handleTestCaseFailure(i, currentTestCaseContainer);

                    // Continue with next action
                    continue;
                }
                // Highlight removal happens at the START of the next iteration (or final cleanup)
            }
            // --- End placeholder ---

            // Final cleanup: Remove highlight from the last executed/highlighted item
            if (currentlyHighlightedItem) {
                 currentlyHighlightedItem.classList.remove('executing-highlight');
            }

            // Log all test case states before finalization
            console.log('Test case states BEFORE finalization:', JSON.parse(JSON.stringify(this.testCaseExecutionStates)));

            // After execution, finalize any 'running' test cases to 'passed'
            // ONLY if they haven't already been marked as 'failed'.
            this.testCaseExecutionStates.forEach(tcState => {
                // Check if this test case has any failed actions in the UI
                const tcContainer = this.actionsListElement.querySelector(`.test-case-container[data-test-idx="${tcState.testIdx}"]`);
                const hasErrorUI = tcContainer ? tcContainer.querySelector('.action-item.error') !== null : false;
                
                if (hasErrorUI) {
                    // If the UI shows errors, force status to 'failed' regardless of current status
                    console.log(`Test case ${tcState.testIdx} has UI errors, marking as FAILED`);
                    this._updateTestCaseState(tcState.testIdx, 'failed', 'UI elements indicate failure');
                }
                else if (tcState.status === 'running') {
                    // Only change running states to passed (with extra safety check)
                    console.log(`Test case ${tcState.testIdx} finalized from 'running' to 'passed'`);
                    this._updateTestCaseState(tcState.testIdx, 'passed');
                }
                else {
                    console.log(`Test case ${tcState.testIdx} status unchanged: ${tcState.status}`);
                }
            });

            // Log all test case states after finalization
            console.log('Test case states AFTER finalization:', JSON.parse(JSON.stringify(this.testCaseExecutionStates)));

            // After execution, collapse all test cases but keep them visible
            this._collapseAllTestCases();

            // Make sure all test case headers remain visible and properly styled
            const allTestCaseHeaders = this.actionsListElement.querySelectorAll('.test-case-header');
            allTestCaseHeaders.forEach(header => {
                const testCaseContainer = header.closest('.test-case-container');
                if (testCaseContainer) {
                    testCaseContainer.style.display = 'block';

                    // Remove current-test-case class from all test cases
                    testCaseContainer.classList.remove('current-test-case');

                    // Check if any actions in this test case have error status
                    const hasErrors = testCaseContainer.querySelectorAll('.action-item.error').length > 0;
                    const hasSuccess = testCaseContainer.querySelectorAll('.action-item.success').length > 0;

                    if (hasErrors) {
                        header.classList.remove('success');
                        header.classList.add('error');
                    } else if (hasSuccess) {
                        header.classList.remove('error');
                        header.classList.add('success');
                    }
                }
            });

            // Check if we have any failures based on internal state
            this.lastExecutionHadFailures = this.testCaseExecutionStates.some(tc => tc.status === 'failed');
            console.log('[executeAllActions] Test Case States:', JSON.parse(JSON.stringify(this.testCaseExecutionStates)));
            console.log('[executeAllActions] lastExecutionHadFailures:', this.lastExecutionHadFailures);

            // Log execution results
            if (this.lastExecutionHadFailures) {
                const failedCount = this.testCaseExecutionStates.filter(tc => tc.status === 'failed').length;
                this.app.logAction('warning', `${failedCount} test${failedCount > 1 ? 's' : ''} failed.`);
                console.log(`${failedCount} tests failed (from internal state)`);
            } else {
                this.app.logAction('success', 'All tests passed successfully!');
                console.log("All tests passed (from internal state)");
            }

            // Update UI for stop
            this.isExecuting = false;
            this._updateUIForStop();

            // Always generate a report, regardless of success or failure
            // This will also save action logs before generating the report
            this.generateExecutionReport();

            // Check for latest report
            setTimeout(() => this.checkForLatestReport(), 2000);

            if (allSucceeded) {
                this.app.logAction('success', 'Execution completed successfully.');
                this.app.showToast('Success', 'All actions executed successfully', 'success');
            } else if (!this.shouldStop) {
                this.app.logAction('error', 'Execution failed but report was generated.');
                this.app.showToast('Warning', 'Execution failed but report was generated. Check logs for details.', 'warning');
            }

        } catch (error) {
            console.error('Error during executeAllActions:', error);
            this.app.logAction('error', `Execution error: ${error.message}`);
            this.app.showToast('Error', `Execution error: ${error.message}`, 'error');
            this.isExecuting = false;
            this._updateUIForStop();
        }
    }



    /**
     * Find all Hook Actions in the actions list, but only within the current test case
     * @param {Array} actions - The list of actions to search
     * @returns {Array} - Array of Hook Actions found, empty array if none
     */
    _findHookActions(actions) {
        if (!actions || !Array.isArray(actions)) {
            return [];
        }

        // Get the current test case container
        const currentTestCaseContainer = this.actionsListElement.querySelector('.test-case-container.current-test-case');

        if (!currentTestCaseContainer) {
            console.log("No current test case container found, using fallback method");
            // Fallback: Find the test case container that contains the currently executing action
            const currentActionItem = this.actionsListElement.querySelector('.action-item.executing-highlight');
            if (currentActionItem) {
                const testCaseContainer = currentActionItem.closest('.test-case-container');
                if (testCaseContainer) {
                    // Mark this as the current test case
                    testCaseContainer.classList.add('current-test-case');
                    console.log("Found current test case container from executing action");
                }
            } else {
                // If no executing action, try to find the action that failed
                const failedActionItem = this.actionsListElement.querySelector('.action-item.error, .action-item.action-item-failed');
                if (failedActionItem) {
                    const testCaseContainer = failedActionItem.closest('.test-case-container');
                    if (testCaseContainer) {
                        // Mark this as the current test case
                        testCaseContainer.classList.add('current-test-case');
                        console.log("Found current test case container from failed action");
                    }
                }
            }
        }

        // Try again to get the current test case container
        const testCaseContainer = this.actionsListElement.querySelector('.test-case-container.current-test-case');

        if (testCaseContainer) {
            // Get the test case ID
            const testCaseId = testCaseContainer.dataset.testCaseId;
            console.log(`Finding hook actions for test case: ${testCaseId}`);

            // Get all action items in this test case
            const actionItems = testCaseContainer.querySelectorAll('.action-item');

            // Get the indices of these action items
            const actionIndices = Array.from(actionItems).map(item => {
                return parseInt(item.dataset.actionIndex, 10);
            });

            // Filter actions to only include those in the current test case
            const testCaseActions = actions.filter((_, index) => {
                return actionIndices.includes(index);
            });

            // Find hook actions within the test case actions
            const hookActions = testCaseActions.filter(action => action.type === 'hookAction');
            console.log(`Found ${hookActions.length} hook actions in test case ${testCaseId}`);

            return hookActions;
        } else {
            console.log("No test case container found, trying to find the most relevant hook actions");

            // First, try to find the currently executing or failed action
            const relevantAction = this.actionsListElement.querySelector('.action-item.executing-highlight, .action-item.error, .action-item.action-item-failed');

            if (relevantAction) {
                // Get the index of this action
                const actionIndex = parseInt(relevantAction.getAttribute('data-action-index'), 10);
                console.log(`Found relevant action at index ${actionIndex}, looking for hook actions in the same test case`);

                // Try to find hook actions that are close to this action
                // This is a heuristic - we assume hook actions are in the same test case
                // and are likely to be within a reasonable range of the current action
                const hookActions = actions.filter((action, index) => {
                    return action.type === 'hookAction' && Math.abs(index - actionIndex) < 20;
                });

                if (hookActions.length > 0) {
                    console.log(`Found ${hookActions.length} hook actions near the relevant action`);
                    return hookActions;
                }
            }

            // If we still couldn't find relevant hook actions, fall back to all hook actions
            console.log("Falling back to all hook actions");
            return actions.filter(action => action.type === 'hookAction');
        }
    }

    /**
     * Find a Hook Action in the actions list (legacy method for backward compatibility)
     * @param {Array} actions - The list of actions to search
     * @returns {Object|null} - The first Hook Action if found, null otherwise
     * @deprecated Use _findHookActions instead
     */
    _findHookAction(actions) {
        if (!actions || !Array.isArray(actions)) {
            return null;
        }

        // Find the first action with type 'hookAction'
        return actions.find(action => action.type === 'hookAction');
    }

    /**
     * Find all failed tests in the current test suite
     * @returns {Array} Array of failed test objects with their actions
     */
    findFailedTests() {
        console.log("Finding failed tests from internal state...");
        const failedTests = this.testCaseExecutionStates
            .filter(tcState => tcState.status === 'failed')
            .map(tcState => {
                // Get the DOM container for UI purposes if needed by rerunFailedTests for highlighting
                const container = this.actionsListElement.querySelector(`.test-case-container[data-test-idx="${tcState.testIdx}"]`);

                return {
                    container: container, // DOM element for UI interaction
                    testIdx: tcState.testIdx, // The original, definitive test index
                    testName: tcState.name,   // Name stored in internal state
                    actions: tcState.actions.map(a => ({ // Map to the structure rerun expects, if needed
                        action: a.action, // The raw action object
                        element: this.actionsListElement.querySelector(`.action-item[data-action-index="${a.originalGlobalIndex}"]`), // Find original DOM element
                        index: a.originalGlobalIndex, // Original global index
                        isFailed: a.status === 'error' // Status of this specific action in the test case
                    })),
                    hasFailedActions: tcState.actions.some(a => a.status === 'error'), // Check if any action within this TC failed
                    filename: tcState.filename
                };
            });

        console.log("Finished finding failed tests from internal state. Result:", JSON.stringify(failedTests.map(t => ({ name: t.testName, testIdx: t.testIdx, actionsCount: t.actions.length, filename: t.filename }))));
        return failedTests;
    }



    /**
     * Collapse all test cases after execution
     * This preserves the headers but hides the actions for a cleaner view
     */
    _collapseAllTestCases() {
        const testCaseContainers = this.actionsListElement.querySelectorAll('.test-case-container');
        testCaseContainers.forEach(container => {
            const actionsContainer = container.querySelector('.test-case-actions');
            const header = container.querySelector('.test-case-header');

            if (actionsContainer && actionsContainer.classList.contains('show')) {
                // Use Bootstrap API to collapse without clicking (to avoid triggering handlers twice)
                const collapseInstance = bootstrap.Collapse.getInstance(actionsContainer);
                if (collapseInstance) {
                    collapseInstance.hide();
                } else {
                    // Fallback to direct DOM manipulation if Bootstrap instance isn't available
                    actionsContainer.classList.remove('show');
                    if (header) {
                        const icon = header.querySelector('.collapse-icon');
                        if (icon) icon.style.transform = 'rotate(-90deg)';
                    }
                }
            }
        });
    }

    /**
     * Stop the current execution (client-side flag)
     */
    stopExecution() {
        console.log("ExecutionManager: stopExecution called");
        if (!this.isExecuting) {
            return;
        }

        this.shouldStop = true;
        this.app.logAction('warning', 'Stop requested. Finishing current action...');

        // Update UI immediately to show stopping state
        if(this.stopBtn) this.stopBtn.disabled = true;
        if(this.stopBtn) this.stopBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Stopping...';

        // Hide the fixed device screen
        if (this.fixedScreenManager) {
            this.fixedScreenManager.unfix();
        }

        // Note: We are not sending a stop signal to the server with fetch.
        // The loop in executeAllActions will check this.shouldStop.
        // A backend endpoint /api/actions/stop could be added for true server-side stop.
    }

    /**
     * Clear visual status indicators from all actions
     */
    _clearAllActionStatuses() {
        if (!this.actionsListElement) return;
        // Use querySelectorAll on .action-item to be more specific
        const actionItems = this.actionsListElement.querySelectorAll('.action-item');
        actionItems.forEach((actionItem) => {
            // Remove all potential status/highlight classes
            actionItem.classList.remove(
                'list-group-item-success',
                'list-group-item-danger',
                'list-group-item-info',
                'bg-opacity-25',
                'action-item-failed', // Also remove failed highlight
                'executing-highlight' // Ensure execution highlight is cleared too
            );
            // Optionally clear status icons if they were used previously
            const statusIconContainer = actionItem.querySelector('.action-status-icon');
            if (statusIconContainer) {
                statusIconContainer.innerHTML = '';
            }

            // Remove any "RUNNING" badges
            const runningBadge = actionItem.querySelector('.running-badge');
            if (runningBadge) {
                runningBadge.remove();
            }

            // Clear multi-step items status if this is a multi-step action
            const multiStepContainer = actionItem.querySelector('.multi-step-container');
            if (multiStepContainer) {
                const multiStepItems = multiStepContainer.querySelectorAll('.multi-step-item');
                multiStepItems.forEach(item => {
                    // Remove all status classes
                    item.classList.remove('multi-step-executing', 'multi-step-success', 'multi-step-error');

                    // Clear status icon
                    const statusElement = item.querySelector('.multi-step-status');
                    if (statusElement) {
                        statusElement.innerHTML = '';
                    }

                    // Remove any tooltips
                    item.removeAttribute('title');
                    item.removeAttribute('data-bs-toggle');
                    item.removeAttribute('data-bs-placement');

                    // Remove any "FAILED STEP" badges
                    const failedStepBadge = item.querySelector('.badge.bg-danger');
                    if (failedStepBadge) {
                        failedStepBadge.remove();
                    }
                });
            }
        });
    }

    /**
     * Update action status in the UI
     * @param {number|Element} indexOrElement - Index of the action or the action element itself
     * @param {string} status - 'running', 'success', 'error', or 'clear'
     * @param {string} [message] - Optional message for logs/tooltips
     */
    _updateActionStatusInUI(indexOrElement, status, message = '') {
        if (!this.actionsListElement) {
            console.error('ExecutionManager: actionsList element not found!');
            return;
        }

        let actionItem;

        // Check if we were passed an element or an index
        if (typeof indexOrElement === 'object' && indexOrElement !== null) {
            // We were passed the element directly
            actionItem = indexOrElement;
        } else {
            // We were passed an index, find the element
            actionItem = this.actionsListElement.querySelector(`.action-item[data-action-index="${indexOrElement}"]`);
        }

        if (actionItem) {
            // Clear previous styling related to status (keep execution highlight)
            actionItem.classList.remove(
                'list-group-item-success',
                'list-group-item-danger',
                'list-group-item-info',
                'list-group-item-warning',
                'list-group-item-secondary',
                'bg-opacity-25',
                'action-item-failed',
                'action-item-recovery',
                'action-item-retrying',
                'success',  // Make sure to remove both success and error classes
                'error'     // to avoid having both applied simultaneously
            );

            let iconHtml = '';

            // Add appropriate styling and icon based on status
            switch (status) {
                case 'running':
                    actionItem.classList.add('list-group-item-info', 'bg-opacity-25');
                    iconHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm" role="status"></span></div>';
                    break;

                case 'recovery':
                    actionItem.classList.add('list-group-item-warning', 'bg-opacity-25', 'action-item-recovery');
                    iconHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm text-warning" role="status"></span></div>';

                    // Add tooltip with recovery message if provided
                    if (message) {
                        actionItem.setAttribute('title', message);
                        actionItem.setAttribute('data-bs-toggle', 'tooltip');
                        actionItem.setAttribute('data-bs-placement', 'top');
                    }
                    break;

                case 'retrying':
                    actionItem.classList.add('list-group-item-info', 'bg-opacity-25', 'action-item-retrying');
                    iconHtml = '<div class="action-status"><span class="spinner-border spinner-border-sm text-primary" role="status"></span></div>';

                    // Add tooltip with retry message if provided
                    if (message) {
                        actionItem.setAttribute('title', message);
                        actionItem.setAttribute('data-bs-toggle', 'tooltip');
                        actionItem.setAttribute('data-bs-placement', 'top');
                    }
                    break;

                case 'success':
                    actionItem.classList.add('success');
                    iconHtml = '<div class="action-status"><i class="bi bi-check-circle"></i></div>';
                    break;

                case 'skipped':
                    actionItem.classList.add('list-group-item-secondary', 'bg-opacity-25');
                    iconHtml = '<div class="action-status"><i class="bi bi-skip-forward-circle text-secondary"></i></div>';

                    // Add tooltip with skip message if provided
                    if (message) {
                        actionItem.setAttribute('title', message);
                        actionItem.setAttribute('data-bs-toggle', 'tooltip');
                        actionItem.setAttribute('data-bs-placement', 'top');
                    }
                    break;

                case 'error':
                    actionItem.classList.add('error', 'action-item-failed');
                    iconHtml = '<div class="action-status"><i class="bi bi-x-circle"></i></div>';

                    // Add tooltip with error message if provided
                    if (message) {
                        actionItem.setAttribute('title', message);
                        actionItem.setAttribute('data-bs-toggle', 'tooltip');
                        actionItem.setAttribute('data-bs-placement', 'top');
                        // Potentially initialize the tooltip, if using Bootstrap 5
                        // new bootstrap.Tooltip(actionItem);
                    }
                    break;

                case 'clear':
                    // Remove all indicators
                    break;
            }

            // Update action status icon if the element exists
            if (iconHtml) {
                let statusElement = actionItem.querySelector('.action-status');
                if (!statusElement) {
                    // If status element doesn't exist, create it before the content
                    const actionContent = actionItem.querySelector('.action-content');
                    if (actionContent) {
                        statusElement = document.createElement('div');
                        statusElement.className = 'action-status-icon';
                        actionItem.insertBefore(statusElement, actionContent);
                    }
                }

                if (statusElement) {
                    statusElement.innerHTML = iconHtml;
                }
            }

            // Make sure the test case container remains visible even after updating status
            const testCaseContainer = actionItem.closest('.test-case-container');
            if (testCaseContainer) {
                testCaseContainer.style.display = 'block';

                // Update test case header status based on action result
                if (status === 'error') {
                    // Mark as error if any action fails
                    const header = testCaseContainer.querySelector('.test-case-header');
                    if (header) {
                        header.classList.remove('success');
                        header.classList.add('error');
                    }
                } else if (status === 'success') {
                    // Check if there are any error actions in this test case
                    const errorActions = testCaseContainer.querySelectorAll('.action-item.error');

                    // Only mark as success if no error actions exist
                    if (errorActions.length === 0) {
                        const header = testCaseContainer.querySelector('.test-case-header');
                        if (header) {
                            header.classList.remove('error');
                            header.classList.add('success');
                        }
                    }
                }
            }

            // If this is a multi-step action, make sure the container is visible
            if (actionItem.classList.contains('multi-step') || actionItem.querySelector('.multi-step-container')) {
                const multiStepContainer = actionItem.querySelector('.multi-step-container');
                if (multiStepContainer) {
                    // Make sure the container is visible
                    multiStepContainer.style.display = 'block';

                    // If the action has a toggle button, make sure it's expanded
                    const toggleButton = actionItem.querySelector('.multi-step-toggle');
                    if (toggleButton && toggleButton.getAttribute('aria-expanded') === 'false') {
                        toggleButton.click(); // Expand the multi-step container
                    }

                    // If this is a running status, make sure all step statuses are cleared
                    if (status === 'running') {
                        const multiStepItems = multiStepContainer.querySelectorAll('.multi-step-item');
                        multiStepItems.forEach(item => {
                            // Remove all status classes
                            item.classList.remove('multi-step-executing', 'multi-step-success', 'multi-step-error');

                            // Clear status icon
                            const statusElement = item.querySelector('.multi-step-status');
                            if (statusElement) {
                                statusElement.innerHTML = '';
                            }

                            // Remove any tooltips
                            item.removeAttribute('title');
                            item.removeAttribute('data-bs-toggle');
                            item.removeAttribute('data-bs-placement');

                            // Remove any "FAILED STEP" badges
                            const failedStepBadge = item.querySelector('.badge.bg-danger');
                            if (failedStepBadge) {
                                failedStepBadge.remove();
                            }
                        });
                    }
                }
            }
        } else {
            // Log a more helpful message based on what was passed
            if (typeof indexOrElement === 'object' && indexOrElement !== null) {
                console.warn(`ExecutionManager: Invalid action element passed to _updateActionStatusInUI`);
            } else {
                console.warn(`ExecutionManager: Action item with index ${indexOrElement} not found in DOM`);
            }
        }
    }

    /**
     * Update UI for execution start
     * @param {boolean} isRerunMode - Whether we're in rerun failed mode
     */
    _updateUIForStart(isRerunMode = false) {
        // Disable execute button, enable stop button
        if(this.executeBtn) this.executeBtn.disabled = true;
        if(this.rerunFailedBtn) this.rerunFailedBtn.disabled = true;
        if(this.stopBtn) {
            this.stopBtn.disabled = false;
            this.stopBtn.classList.remove('btn-secondary');
            this.stopBtn.classList.add('btn-danger');
        }

        // Show stop buttons for test cases if we're in a test suite context
        if (this.app && typeof this.app.showStopButtons === 'function') {
            this.app.showStopButtons();
        }

        // Disable other elements like action form, etc.
        const actionType = document.getElementById('actionType');
        if(actionType) actionType.disabled = true;

        const addAction = document.getElementById('addAction');
        if(addAction) addAction.disabled = true;

        // If we're in rerun failed mode, add the rerun-failed-mode class to the actions list
        if (isRerunMode && this.actionsListElement) {
            this.actionsListElement.classList.add('rerun-failed-mode');

            // Add a badge to indicate we're in rerun failed mode
            let rerunBadge = document.getElementById('rerunFailedBadge');
            if (!rerunBadge) {
                rerunBadge = document.createElement('div');
                rerunBadge.id = 'rerunFailedBadge';
                rerunBadge.className = 'rerun-failed-badge';
                rerunBadge.textContent = 'RERUN FAILED MODE';
                document.body.appendChild(rerunBadge);
            }
        }
        
        // Dispatch execution-started event for other components to listen to
        document.dispatchEvent(new Event('execution-started'));
    }

    /**
     * Update UI for execution stop
     */
    _updateUIForStop() {
        // Re-enable execute button, disable stop button
        if (this.executeBtn) this.executeBtn.disabled = false;

        // Hide stop buttons for test cases
        if (this.app && typeof this.app.hideStopButtons === 'function') {
            this.app.hideStopButtons();
        }

        // Enable/disable rerun button based on whether there are failures
        // Ensure this check uses the most up-to-date failure status
        this.lastExecutionHadFailures = this.testCaseExecutionStates.some(tc => tc.status === 'failed');
        console.log('[_updateUIForStop] Test Case States before Rerun Button Check:', JSON.parse(JSON.stringify(this.testCaseExecutionStates)));
        console.log('[_updateUIForStop] lastExecutionHadFailures:', this.lastExecutionHadFailures);

        if (this.rerunFailedBtn) {
            this.rerunFailedBtn.disabled = !this.lastExecutionHadFailures;
            if (this.lastExecutionHadFailures) {
                this.app.logAction('warning', `${this.lastExecutionHadFailures} test${this.lastExecutionHadFailures > 1 ? 's' : ''} failed. You can use the "Rerun Failed" button to retry only the failed tests.`);
            } else {
                this.app.logAction('success', 'All previously failed tests passed after rerun!');
            }
        }

        if (this.stopBtn) {
            this.stopBtn.disabled = true;
            this.stopBtn.classList.remove('btn-danger');
            this.stopBtn.classList.add('btn-secondary');
            this.stopBtn.innerHTML = '<i class="bi bi-stop-fill"></i> Stop Execution';
        }

        // Re-enable other elements
        const actionType = document.getElementById('actionType');
        if (actionType) actionType.disabled = false;

        const addAction = document.getElementById('addAction');
        if (addAction) addAction.disabled = false;

        // Remove rerun failed mode class and badge
        if (this.actionsListElement) {
            this.actionsListElement.classList.remove('rerun-failed-mode');
        }

        // Remove the rerun failed badge
        const rerunBadge = document.getElementById('rerunFailedBadge');
        if (rerunBadge) {
            rerunBadge.remove();
        }

        // Stop periodic health checks
        this.stopHealthChecks();

        // Hide the fixed device screen
        if (this.fixedScreenManager) {
            this.fixedScreenManager.unfix();
        }
        
        // Dispatch execution-completed event for other components to listen to
        document.dispatchEvent(new Event('execution-completed'));
    }

    /**
     * Check for the latest report and show a notification if available
     */
    async checkForLatestReport() {
        try {
            // Wait a moment to ensure the report is generated
            await new Promise(resolve => setTimeout(resolve, 1000));

            const response = await fetch('/api/reports/latest');
            const data = await response.json();

            if (data.status === 'success' && data.report_url) {
                // Use test suites manager's report button if available
                if (window.testSuitesManager && typeof window.testSuitesManager.showReportButton === 'function') {
                    window.testSuitesManager.showReportButton(data.report_url, 'Test Suite');
                }

                // Reports refresh code removed as it's no longer needed
            }
        } catch (error) {
            console.error('Error checking for report:', error);
        }
    }

    /**
     * Generate an execution report for the current test run
     */
    async generateExecutionReport() {
        try {
            this.app.logAction('info', 'Generating execution report...');

            // First, save action logs to ensure they're included in the report
            await this._saveActionLogs();

            // Collect all actions and their statuses
            const actions = this.app.currentActions;
            const actionItems = Array.from(this.actionsListElement.querySelectorAll('.action-item'));

            // Get test case names from the UI
            const testCaseContainers = Array.from(this.actionsListElement.querySelectorAll('.test-case-container'));
            const testCases = [];

            // Group actions by test case
            testCaseContainers.forEach(container => {
                const headerElement = container.querySelector('.test-case-header');
                const testCaseName = headerElement ? headerElement.textContent.trim() : 'Test Case';

                // Get all action items within this test case
                const actionElements = Array.from(container.querySelectorAll('.action-item'));

                // Only process if we have actions
                if (actionElements.length > 0) {
                    // Get actual actions using the data-action-index attribute
                    const testCaseActions = actionElements.map(element => {
                        const index = parseInt(element.getAttribute('data-action-index'));
                        return {
                            index,
                            element,
                            action: actions[index]
                        };
                    }).filter(item => item.action); // Make sure we have valid actions

                    // Calculate status - check if any actions are still in error state
                    // Note: If all errors were fixed by hook actions, there should be no elements with 'error' class
                    let hasErrors = actionElements.some(el => el.classList.contains('error'));
                    let hasSuccess = actionElements.some(el => el.classList.contains('success'));

                    // Also check the test case header status which is more reliable
                    const header = container.querySelector('.test-case-header');
                    const headerHasError = header ? header.classList.contains('error') : false;
                    const headerHasSuccess = header ? header.classList.contains('success') : false;

                    let status = 'unknown';
                    if (hasErrors || headerHasError) status = 'failed';
                    else if (hasSuccess || headerHasSuccess) status = 'passed';

                    // Create test case object with its actions
                    testCases.push({
                        name: testCaseName,
                        status,
                        actions: testCaseActions
                    });
                }
            });

            // If we have no test cases from UI grouping, create a single test case with all actions
            if (testCases.length === 0 && actions.length > 0) {
                testCases.push({
                    name: 'Test Case',
                    status: 'passed',
                    actions: actions.map((action, index) => ({
                        index,
                        element: actionItems.find(el => parseInt(el.getAttribute('data-action-index')) === index),
                        action
                    }))
                });
            }

            // Create test data in the format expected by the backend
            const testSuiteData = {
                name: 'UI Execution ' + new Date().toLocaleString(),
                testCases: [],
                passed: 0,
                failed: 0,
                skipped: 0
            };

            // Process each test case
            testCases.forEach(testCase => {
                const testCaseData = {
                    name: testCase.name,
                    status: testCase.status,
                    steps: []
                };

                // Count successes/failures for this test case
                let passedSteps = 0;
                let failedSteps = 0;

                // Add each action as a step
                testCase.actions.forEach(({element, action}) => {
                    // Determine status from the UI
                    let status = 'unknown';
                    if (element) {
                        // Check for success class first - this will catch actions that were retried and succeeded
                        if (element.classList.contains('success')) {
                            status = 'passed';
                            passedSteps++;
                        } else if (element.classList.contains('error')) {
                            status = 'failed';
                            failedSteps++;
                        }
                    }

                    // Add to steps
                    const stepData = {
                        name: this.app.actionManager.getActionDescription(action),
                        status: status,
                        duration: action.executionTime || '0ms' // Use captured execution time if available
                    };

                    // Add screenshot_filename if available
                    if (action.screenshot_filename) {
                        stepData.screenshot_filename = action.screenshot_filename;
                        console.log(`Added screenshot_filename to step: ${action.screenshot_filename}`);
                    }

                    // Get screenshot_filename from data attribute if available
                    if (element) {
                        const screenshotFilename = element.getAttribute('data-screenshot-filename');
                        if (screenshotFilename && !stepData.screenshot_filename) {
                            stepData.screenshot_filename = screenshotFilename;
                            console.log(`Added screenshot_filename from data attribute: ${screenshotFilename}`);
                        }
                    }

                    testCaseData.steps.push(stepData);
                });

                // Add test case to suite
                testSuiteData.testCases.push(testCaseData);

                // Update suite counts
                if (testCase.status === 'passed') testSuiteData.passed++;
                else if (testCase.status === 'failed') testSuiteData.failed++;
                else testSuiteData.skipped++;
            });

            // Set overall status
            testSuiteData.status = testSuiteData.failed > 0 ? 'failed' : 'passed';

            // Only generate report for test suites with multiple test cases OR when a test suite is loaded
            if (testSuiteData.testCases.length <= 1 || !this.app.isTestSuiteLoaded) {
                if (!this.app.isTestSuiteLoaded) {
                    this.app.logAction('info', 'Skipping report generation - individual test case execution (not from test suite)');
                } else {
                    this.app.logAction('info', 'Skipping report generation - only single test case executed');
                }
                return;
            }

            // Send to backend to generate report
            const response = await fetch('/api/generate_report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testSuiteData)
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.app.logAction('success', 'Execution report generated successfully');

                // Wait a moment to ensure the report is fully written to disk before dispatching the event
                setTimeout(() => {
                    console.log('ExecutionManager: Dispatching execution-completed event after report generation');
                    document.dispatchEvent(new Event('execution-completed'));
                }, 2000); // Wait 2 seconds to ensure report is fully generated
                
                // Delete screenshots after test execution completes and report is generated
                try {
                    this.app.logAction('info', 'Cleaning up screenshots...');
                    const deleteResponse = await fetch('/api/screenshots/delete_all', {
                        method: 'POST'
                    });

                    const deleteResult = await deleteResponse.json();

                    if (deleteResult.status === 'success') {
                        this.app.logAction('success', 'All screenshots deleted successfully');
                    } else {
                        this.app.logAction('warning', `Failed to delete screenshots: ${deleteResult.message || 'Unknown error'}`);
                    }
                } catch (deleteError) {
                    console.error('Error deleting screenshots:', deleteError);
                    this.app.logAction('warning', `Error deleting screenshots: ${deleteError.message}`);
                }
            } else {
                this.app.logAction('warning', 'Failed to generate execution report');
            }
        } catch (error) {
            console.error('Error generating report:', error);
            this.app.logAction('error', `Error generating report: ${error.message}`);
        }
    }

    /**
     * Save action logs to the server
     * @returns {Promise} A promise that resolves when the logs are saved
     */
    async _saveActionLogs() {
        try {
            // Get all action logs from the UI
            const actionLogContainer = document.getElementById('actionLog');
            if (actionLogContainer) {
                const logEntries = [];
                const logItems = actionLogContainer.querySelectorAll('.log-entry');

                logItems.forEach(item => {
                    // Extract timestamp from the log-time span
                    const timestamp = item.querySelector('.log-time')?.textContent || '';

                    // Determine the type based on the log entry class
                    const type = item.classList.contains('log-success') ? 'success' :
                                item.classList.contains('log-error') ? 'error' :
                                item.classList.contains('log-warning') ? 'warning' : 'info';

                    // Extract message from the log-message span
                    const message = item.querySelector('.log-message')?.textContent || '';

                    // Extract action_id from the message if it exists
                    let actionId = '';
                    const actionIdMatch = message.match(/\(action_id: ([^)]+)\)/);
                    if (actionIdMatch && actionIdMatch[1]) {
                        actionId = actionIdMatch[1];
                    }

                    logEntries.push({
                        timestamp,
                        type,
                        message,
                        action_id: actionId // Include action_id in the log entry
                    });
                });

                if (logEntries.length > 0) {
                    this.app.logAction('info', `Saving ${logEntries.length} action log entries to file...`);

                    const response = await fetch('/api/logs/save', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            logs: logEntries
                        })
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        this.app.logAction('success', 'Action logs saved successfully');
                    } else {
                        this.app.logAction('warning', `Failed to save action logs: ${result.message || 'Unknown error'}`);
                    }
                } else {
                    this.app.logAction('info', 'No action logs to save');
                }
            }
        } catch (error) {
            console.error('Error saving action logs:', error);
            this.app.logAction('warning', `Error saving action logs: ${error.message}`);
        }
    }

    /**
     * Execute hook actions when a step fails
     * @param {Array} hookActions - The hook actions to execute
     * @param {string} testCaseId - The ID of the test case that failed
     * @returns {Promise<Object>} - Result of hook action execution
     */
    async executeHookActions(hookActions, testCaseId) {
        if (!hookActions || hookActions.length === 0) {
            return {
                success: false,
                message: "No hook actions provided"
            };
        }

        this.app.logAction('info', `Executing ${hookActions.length} hook actions for recovery...`);

        try {
            // Call the API to execute hook actions
            const result = await this.app.fetchApi('action/execute_hook', 'POST', {
                actions: hookActions,
                test_case_id: testCaseId || '',
                force_screenshot: true
            });

            return result;
        } catch (error) {
            console.error('Error executing hook actions:', error);
            return {
                success: false,
                message: `Error executing hook actions: ${error.message}`
            };
        }
    }

    /**
     * Handle multi-step action during rerun
     * @param {Element} actionElement - The multi-step action element
     * @param {Object} action - The multi-step action data
     * @param {boolean} success - Whether the action succeeded
     * @param {string} [errorMessage] - Error message if the action failed
     * @returns {Promise<void>}
     */
    async _handleMultiStepActionDuringRerun(actionElement, action, success, errorMessage = '') {
        console.log(`Handling multi-step action during rerun: ${action.test_case_name}, success: ${success}`);

        // Make sure the multi-step container is visible
        const multiStepContainer = actionElement.querySelector('.multi-step-container');
        if (!multiStepContainer) {
            console.error('Multi-step container not found in the DOM');
            return;
        }

        // Make sure the container is visible
        multiStepContainer.style.display = 'block';

        // If the action has a toggle button, make sure it's expanded
        const toggleButton = actionElement.querySelector('.multi-step-toggle');
        if (toggleButton && toggleButton.getAttribute('aria-expanded') === 'false') {
            toggleButton.click(); // Expand the multi-step container
        }

        // Get the test case steps
        const testCaseSteps = action.test_case_steps || [];
        console.log(`Multi-step action has ${testCaseSteps.length} steps`);

        if (success) {
            // Mark all steps as success
            for (let j = 0; j < testCaseSteps.length; j++) {
                const stepItem = multiStepContainer.querySelector(`.multi-step-item[data-step-index="${j}"]`);
                if (stepItem) {
                    // Create status element if it doesn't exist
                    let stepStatus = stepItem.querySelector('.multi-step-status');
                    if (!stepStatus) {
                        stepStatus = document.createElement('div');
                        stepStatus.className = 'multi-step-status';
                        stepItem.appendChild(stepStatus);
                    }

                    // Show success icon
                    stepStatus.innerHTML = '<i class="bi bi-check-circle text-success"></i>';

                    // Update classes
                    stepItem.classList.remove('multi-step-executing', 'multi-step-error');
                    stepItem.classList.add('multi-step-success');
                }
            }
        } else {
            // Find the failed step (assume it's the last one)
            let failedStepIndex = testCaseSteps.length - 1;

            // Mark all steps up to the failed one as success, and the failed one as error
            for (let j = 0; j < testCaseSteps.length; j++) {
                const stepItem = multiStepContainer.querySelector(`.multi-step-item[data-step-index="${j}"]`);
                if (stepItem) {
                    // Create status element if it doesn't exist
                    let stepStatus = stepItem.querySelector('.multi-step-status');
                    if (!stepStatus) {
                        stepStatus = document.createElement('div');
                        stepStatus.className = 'multi-step-status';
                        stepItem.appendChild(stepStatus);
                    }

                    if (j < failedStepIndex) {
                        // Show success icon for completed steps
                        stepStatus.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                        stepItem.classList.remove('multi-step-executing', 'multi-step-error');
                        stepItem.classList.add('multi-step-success');
                    } else if (j === failedStepIndex) {
                        // Show error icon for failed step
                        stepStatus.innerHTML = '<i class="bi bi-x-circle text-danger"></i>';
                        stepItem.classList.remove('multi-step-executing', 'multi-step-success');
                        stepItem.classList.add('multi-step-error');

                        // Add "FAILED STEP" indicator
                        const failedStepIndicator = document.createElement('span');
                        failedStepIndicator.className = 'badge bg-danger ms-2';
                        failedStepIndicator.textContent = 'FAILED STEP';

                        // Add to content if it exists, otherwise to the step item
                        const content = stepItem.querySelector('.multi-step-content');
                        if (content) {
                            content.appendChild(failedStepIndicator);
                        } else {
                            stepItem.appendChild(failedStepIndicator);
                        }

                        // Add tooltip with error message
                        if (errorMessage) {
                            stepItem.setAttribute('title', errorMessage);
                            stepItem.setAttribute('data-bs-toggle', 'tooltip');
                            stepItem.setAttribute('data-bs-placement', 'top');
                        }

                        // Scroll to the failed step
                        stepItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    } else {
                        // Skip steps after the failed one
                        stepStatus.innerHTML = '<i class="bi bi-dash-circle text-secondary"></i>';
                        stepItem.classList.remove('multi-step-executing', 'multi-step-success', 'multi-step-error');
                    }
                }
            }
        }
    }

    /**
     * Check if an action is a failed action
     * @param {Element} actionElement - The action element to check
     * @returns {boolean} True if the action is a failed action
     */
    _isFailedAction(actionElement) {
        if (!actionElement) return false;

        return actionElement.classList.contains('error') ||
               actionElement.querySelector('.bi-x-circle') !== null ||
               actionElement.classList.contains('list-group-item-danger') ||
               actionElement.classList.contains('action-item-failed');
    }

    /**
     * Handle test failure by skipping to the next test case, but preserve cleanup steps
     * @param {number} currentIndex - The current action index
     * @param {Object} currentTestCaseContainer - The current test case DOM container
     * @returns {number} The index of the last action in the current test case (or last non-cleanup action)
     */
    _handleTestCaseFailure(currentIndex, currentTestCaseContainer) {
        if (!currentTestCaseContainer) {
            return currentIndex;
        }

        // MOST IMPORTANT: Find the testIdx for this container and explicitly set it to 'failed'
        const testIdx = parseInt(currentTestCaseContainer.dataset.testIdx || '-1');
        if (testIdx !== -1) {
            console.log(`_handleTestCaseFailure: Explicitly setting testIdx ${testIdx} to 'failed'`);
            this._updateTestCaseState(testIdx, 'failed', `Failed at action index ${currentIndex}`);

            // Also make sure at least one action in the UI has the 'error' class
            // This ensures our UI-based check in the finalization loop works
            const failedAction = currentTestCaseContainer.querySelector(`.action-item[data-action-index="${currentIndex}"]`);
            if (failedAction && !failedAction.classList.contains('error')) {
                console.log(`Adding 'error' class to action ${currentIndex} in test case ${testIdx}`);
                failedAction.classList.add('error', 'action-item-failed');
            }
        } else {
            console.warn(`_handleTestCaseFailure: Could not determine testIdx from container, cannot update internal state`);
        }

        // Find all actions in this test case
        const currentTestCaseActions = Array.from(currentTestCaseContainer.querySelectorAll('.action-item'));
        if (currentTestCaseActions.length > 0) {
            // Look for cleanup steps - they should never be skipped
            let lastNonCleanupActionIndex = currentIndex;
            let hasCleanupSteps = false;

            // Check each action in this test case to find cleanup steps
            for (const actionElement of currentTestCaseActions) {
                const actionIndex = parseInt(actionElement.getAttribute('data-action-index'));

                // Only look at actions after the current failed action
                if (actionIndex > currentIndex) {
                    // Look for the action type badge (the second badge after step number)
                    const badges = actionElement.querySelectorAll('.badge');
                    let actionType = '';

                    // The action type is typically the second badge (after step number)
                    if (badges.length >= 2) {
                        actionType = badges[1].textContent.trim();
                    }

                    // Check if this is a cleanup step (check both display text and action type)
                    if (actionType === 'cleanupSteps' || actionType === 'Cleanup Steps') {
                        hasCleanupSteps = true;
                        console.log(`_handleTestCaseFailure: Found cleanup step at index ${actionIndex}, will not skip`);
                        break;
                    } else {
                        lastNonCleanupActionIndex = actionIndex;
                    }
                }
            }

            if (hasCleanupSteps) {
                // Skip to the action just before cleanup steps
                if (lastNonCleanupActionIndex > currentIndex) {
                    this.app.logAction('info', `Skipping remaining steps in failed test case (moving from action ${currentIndex+1} to ${lastNonCleanupActionIndex+1}), but preserving cleanup steps`);
                    return lastNonCleanupActionIndex;
                } else {
                    // Cleanup steps are immediately after the failed action, don't skip anything
                    this.app.logAction('info', `Cleanup steps found immediately after failed action ${currentIndex+1}, continuing execution`);
                    return currentIndex;
                }
            } else {
                // No cleanup steps found, skip to the end of the test case as before
                const lastActionIndex = parseInt(currentTestCaseActions[currentTestCaseActions.length - 1].getAttribute('data-action-index'));
                if (lastActionIndex > currentIndex) {
                    this.app.logAction('info', `Skipping remaining steps in failed test case (moving from action ${currentIndex+1} to next test case at ${lastActionIndex+1})`);
                    return lastActionIndex;
                }
            }
        }

        return currentIndex;
    }

    /**
     * Automatically rerun failed tests up to the max retry limit
     * @returns {Promise<void>}
     * @private
     */
    async _autoRerunFailedTests() {
        // Set flag to prevent nested auto-reruns
        this._isAutoRerunning = true;
        
        try {
            // Get max retry count from global settings (default to 2 if not found)
            const maxRetries = this.getGlobalValue('Test Run Retry', 2);
            // Start with retry count of 1 since this is the first retry
            let retryCount = 1;
            
            this.app.logAction('info', `Auto-rerun of failed tests enabled (will retry up to ${maxRetries} times)`);
            
            while (retryCount <= maxRetries) {
                // Check if we have any failures
                const failedTests = this.findFailedTests();
                if (failedTests.length === 0) {
                    this.app.logAction('success', `All tests passed after ${retryCount - 1} auto-retries. No further retries needed.`);
                    break;
                }
                
                this.app.logAction('info', `Auto-retry ${retryCount}/${maxRetries}: Rerunning ${failedTests.length} failed test${failedTests.length > 1 ? 's' : ''}...`);
                
                // Show notification to the user
                this.app.showToast(
                    'Auto-Retry',
                    `Auto-retry ${retryCount}/${maxRetries}: Rerunning ${failedTests.length} failed test${failedTests.length > 1 ? 's' : ''}...`,
                    'info'
                );
                
                // Wait a moment to let the UI update and ensure execution flags are reset
                console.log(`_autoRerunFailedTests: Waiting for UI update before retry ${retryCount}, current execution state: isExecuting=${this.isExecuting}`);
                await new Promise(resolve => setTimeout(resolve, 1500)); // Increased delay to ensure UI has time to update
                
                // CRITICAL FIX: Force reset execution state before each retry
                // This ensures rerunFailedTests won't return early due to isExecuting flag
                console.log(`_autoRerunFailedTests: Resetting execution state before retry ${retryCount}`);
                this.isExecuting = false;
                this._updateUIForStop();
                
                // Wait a moment after updating UI for stop
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Execute the rerun
                console.log(`_autoRerunFailedTests: Starting retry ${retryCount}`);
                await this.rerunFailedTests(true); // Pass true to indicate this is an auto-rerun
                
                // Wait for a moment after execution completes
                console.log(`_autoRerunFailedTests: Completed retry ${retryCount}, execution result state: isExecuting=${this.isExecuting}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Move to next retry
                retryCount++;
            }
            
            // After max retries, check if we still have failures
            const remainingFailures = this.findFailedTests();
            if (remainingFailures.length > 0) {
                this.app.logAction('warning', `Reached maximum retry limit (${maxRetries}). ${remainingFailures.length} test${remainingFailures.length > 1 ? 's' : ''} still failing.`);
            } else {
                this.app.logAction('success', `All tests passed after ${retryCount - 1} auto-retries.`);
            }
        } catch (error) {
            console.error('Error during auto-rerun process:', error);
            this.app.logAction('error', `Error during auto-rerun process: ${error.message}`);
        } finally {
            // Ensure we clean up the auto-rerun flag regardless of success/failure
            console.log('_autoRerunFailedTests: Cleaning up auto-rerun flag');
            this._isAutoRerunning = false;
            
            // Double-check that execution state is reset properly
            if (this.isExecuting) {
                console.log('_autoRerunFailedTests: Force resetting execution state after auto-rerun completion');
                this.isExecuting = false;
                this._updateUIForStop();
            }
        }
    }
}

// Global instance (consider better scoping if needed)
// let executionManagerInstance;

// Initialization logic should now happen in main.js after AppiumAutomationApp is created
/*
document.addEventListener('DOMContentLoaded', () => {
    // Assume socket is initialized elsewhere (e.g., in app.js or main.js)
    if (window.socket) {
        executionManagerInstance = new ExecutionManager(window.socket);

        // Add event listeners to buttons
        const executeAllBtn = document.getElementById('executeActions');
        const stopExecutionBtn = document.getElementById('stopExecution');

        if (executeAllBtn) {
            executeAllBtn.addEventListener('click', () => executionManagerInstance.executeAllActions());
        }

        if (stopExecutionBtn) {
            stopExecutionBtn.addEventListener('click', () => executionManagerInstance.stopExecution());
        }
    } else {
        console.error('Socket not initialized, ExecutionManager cannot start.');
    }
});
*/

// Helper methods for internal state management (ADD THESE)
ExecutionManager.prototype._initializeTestCaseStates = function() {
    this.testCaseExecutionStates = [];
    const testCaseContainers = this.actionsListElement.querySelectorAll('.test-case-container');
    let globalActionCounter = 0;

    testCaseContainers.forEach((container) => {
        const testIdx = parseInt(container.dataset.testIdx || '-1');
        const filename = container.dataset.filename || null;
        const headerElement = container.querySelector('.test-case-header .test-case-name');
        const name = headerElement ? headerElement.textContent.trim() : `Test Case ${testIdx}`;
        
        const tcActions = [];
        const actionItems = container.querySelectorAll('.action-item');
        actionItems.forEach(actionItem => {
            const actionIndex = parseInt(actionItem.dataset.actionIndex || '-1');
            if (actionIndex !== -1 && this.app.currentActions[actionIndex]) {
                tcActions.push({
                    originalGlobalIndex: actionIndex, // Store the original global index from this.app.currentActions
                    action: JSON.parse(JSON.stringify(this.app.currentActions[actionIndex])), // Deep copy of action
                    status: 'pending', // 'pending', 'running', 'success', 'error' for individual action
                    error: null
                });
            }
        });

        if (testIdx !== -1) {
            this.testCaseExecutionStates.push({
                testIdx: testIdx,
                name: name,
                filename: filename,
                status: 'pending', // 'pending', 'running', 'passed', 'failed', 'rerunning_pending', 'rerunning'
                error: null,
                actions: tcActions, // Array of action states for this test case
                domContainer: container // Keep a reference to the DOM container
            });
        }
        globalActionCounter += actionItems.length;
    });
    console.log('Initialized internal test case states:', this.testCaseExecutionStates);
};

ExecutionManager.prototype._updateTestCaseState = function(testIdx, status, error = null) {
    const tcState = this.testCaseExecutionStates.find(s => s.testIdx === testIdx);
    if (tcState) {
        console.log(`Updating TC State for testIdx ${testIdx}: Old Status: ${tcState.status}, New Status: ${status}, Error: ${error}`);
        tcState.status = status;
        if (error) tcState.error = error;

        // Update UI header
        const header = tcState.domContainer ? tcState.domContainer.querySelector('.test-case-header') : null;
        if (header) {
            header.classList.remove('success', 'error', 'running'); // Clear all
            if (status === 'passed') {
                header.classList.add('success');
            } else if (status === 'failed') {
                header.classList.add('error');
            } else if (status === 'running' || status === 'rerunning') {
                // Optionally add a 'running' class for visual feedback on the header too
                 header.classList.add('running');
            }
        }
    } else {
        console.warn(`_updateTestCaseState: Could not find test case state for testIdx ${testIdx}, creating it`);

        // Find the test case container for this testIdx
        const testCaseContainer = this.actionsListElement.querySelector(`.test-case-container[data-test-idx="${testIdx}"]`);
        if (testCaseContainer) {
            // Create a new test case state
            const newTcState = {
                testIdx: testIdx,
                status: status,
                error: error,
                domContainer: testCaseContainer,
                filename: testCaseContainer.dataset.filename || null
            };

            this.testCaseExecutionStates.push(newTcState);
            console.log(`Created new test case state for testIdx ${testIdx}:`, newTcState);

            // Update UI header
            const header = testCaseContainer.querySelector('.test-case-header');
            if (header) {
                header.classList.remove('success', 'error', 'running'); // Clear all
                if (status === 'passed') {
                    header.classList.add('success');
                } else if (status === 'failed') {
                    header.classList.add('error');
                } else if (status === 'running' || status === 'rerunning') {
                    header.classList.add('running');
                }
            }
        } else {
            console.error(`_updateTestCaseState: Could not find test case container for testIdx ${testIdx}`);
        }
    }
};