import os
import traceback
import time
import math
import base64
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class TapIfImageExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Tap If Image Exists action.
        This action will tap on an image if it exists on the screen, and do nothing if it doesn't.

        Args:
            params (dict): Parameters for the action
                - image_filename (str): The filename of the reference image
                - threshold (float, optional): Similarity threshold (0.0-1.0). Default is 0.7
                - timeout (int, optional): Maximum time to wait for the image to appear in seconds. Default is 5

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
        """
        try:
            # Check if controller is available
            if not self.controller:
                return {"status": "error", "message": "Device controller not available"}

            # Get parameters
            image_filename = params.get('image_filename')
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 5))

            if not image_filename:
                return {"status": "error", "message": "Image filename is required"}

            # Resolve the image path properly (EXACT same logic as Tap Image action)
            abs_path = image_filename
            if not os.path.exists(image_filename):
                # Try to resolve from reference_images directory
                try:
                    from config import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                        if os.path.exists(full_path):
                            abs_path = full_path
                            self.logger.info(f"Resolved image path to: {abs_path}")
                        else:
                            # Try directly in reference_images folder
                            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                            if os.path.exists(ref_path):
                                abs_path = ref_path
                                self.logger.info(f"Resolved image path to: {abs_path}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not import config: {e}")
                    # Fallback to default reference_images directory
                    reference_images_dir = os.path.join(os.getcwd(), 'reference_images')
                    fallback_path = os.path.join(reference_images_dir, os.path.basename(image_filename))
                    if os.path.exists(fallback_path):
                        abs_path = fallback_path
                        self.logger.info(f"Resolved image path to fallback: {abs_path}")

            # Final check if the image exists
            if not os.path.exists(abs_path):
                return {"status": "error", "message": f"Reference image not found: {image_filename}"}

            # Get absolute path for more reliable loading (same as tap_action.py)
            abs_path = os.path.abspath(abs_path)
            self.logger.info(f"Using absolute image path: {abs_path}")
            self.logger.info(f"Looking for image: {os.path.basename(abs_path)} with threshold: {threshold}")

            # Try to find the image using multiple methods (same as regular Tap action)
            image_found = False
            tap_coordinates = None

            # METHOD 1: Try Airtest Template and exists() - This is the PRIMARY method used by Tap Image
            try:
                from airtest.core.api import exists
                from airtest.core.cv import Template
                from airtest.core.error import TargetNotFoundError

                self.logger.info(f"Using Airtest Template and exists() method (primary method)")

                # Create template and check if it exists (same as Tap Image action)
                template_image = Template(abs_path, threshold=threshold)
                self.logger.info(f"Created Template object: {template_image}")

                # Use exists() to find the image (same as Tap Image action)
                match_pos = exists(template_image)

                if match_pos:
                    self.logger.info(f"Image found using Airtest exists() at position: {match_pos}")
                    image_found = True
                    tap_coordinates = match_pos
                else:
                    self.logger.info(f"Image not found using Airtest exists() method")

            except Exception as e:
                self.logger.warning(f"Airtest method failed: {e}")
                if "No devices added" in str(e):
                    self.logger.info("Airtest not initialized, trying fallback methods")
                else:
                    self.logger.warning(f"Airtest error: {e}")

            # METHOD 2: Try controller's find_image method if Airtest failed
            if not image_found and hasattr(self.controller, 'find_image'):
                try:
                    self.logger.info("Trying controller's find_image method")
                    match_pos = self.controller.find_image(abs_path, threshold=threshold, timeout=timeout)
                    if match_pos:
                        self.logger.info(f"Image found using controller's find_image at position: {match_pos}")
                        image_found = True
                        tap_coordinates = match_pos
                    else:
                        self.logger.info("Image not found using controller's find_image method")
                except Exception as e:
                    self.logger.warning(f"Controller's find_image method failed: {e}")

            # METHOD 3: Try OpenCV directly for image recognition (same as Tap action)
            if not image_found:
                try:
                    self.logger.info(f"Trying OpenCV directly for image recognition: {abs_path}")
                    import cv2
                    import numpy as np
                    from PIL import Image
                    import io

                    # Take a screenshot using Appium
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Get device dimensions first
                        device_width = None
                        device_height = None
                        if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                            device_width = self.controller.device_dimensions.get('width')
                            device_height = self.controller.device_dimensions.get('height')
                            self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                        # Get screenshot as base64
                        screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                        screenshot_data = base64.b64decode(screenshot_base64)

                        # Convert to PIL Image first
                        screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                        original_size = screenshot_pil.size
                        self.logger.info(f"Original screenshot size: {original_size[0]}x{original_size[1]}")

                        # Use original screenshot dimensions
                        self.logger.info(f"Using original screenshot dimensions: {original_size[0]}x{original_size[1]}")
                        device_width = original_size[0]
                        device_height = original_size[1]

                        # Convert to OpenCV format
                        screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                        # Load the template image
                        template = cv2.imread(abs_path)
                        if template is not None:
                            # Get template dimensions
                            h, w = template.shape[:2]
                            self.logger.info(f"Template dimensions: {w}x{h}")

                            # Use a lower threshold for OpenCV matching
                            opencv_threshold = max(0.5, threshold - 0.2)
                            self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                            # Try multiple template matching methods
                            methods = [
                                (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                                (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                                (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                            ]

                            best_val = 0
                            best_loc = None
                            best_method = None

                            for method, method_name in methods:
                                result = cv2.matchTemplate(screenshot_cv, template, method)

                                if method == cv2.TM_SQDIFF_NORMED:
                                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                    curr_val = 1.0 - min_val
                                    curr_loc = min_loc
                                else:
                                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                    curr_val = max_val
                                    curr_loc = max_loc

                                self.logger.info(f"Template matching with {method_name}: {curr_val}")

                                if curr_val > best_val:
                                    best_val = curr_val
                                    best_loc = curr_loc
                                    best_method = method_name

                            self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                            if best_val >= opencv_threshold:
                                # Match found, calculate center coordinates
                                x = best_loc[0] + w // 2
                                y = best_loc[1] + h // 2

                                self.logger.info(f"Found image at ({x}, {y}) in original screenshot using OpenCV")
                                image_found = True
                                tap_coordinates = (x, y)
                            else:
                                self.logger.info(f"Image not found with OpenCV (best_val: {best_val}, threshold: {opencv_threshold})")
                        else:
                            self.logger.error(f"Failed to load template image: {abs_path}")

                except ImportError as e:
                    self.logger.warning(f"OpenCV not available: {e}")
                except Exception as e:
                    self.logger.warning(f"Error using OpenCV for image recognition: {e}")

            # If image was found, tap on it
            if image_found and tap_coordinates:
                # Validate coordinates to prevent infinity or NaN values
                device_width = None
                device_height = None

                # Try to get device dimensions if available
                if hasattr(self.controller, 'get_device_size'):
                    try:
                        device_size = self.controller.get_device_size()
                        if device_size and len(device_size) == 2:
                            device_width, device_height = device_size
                    except Exception as size_err:
                        self.logger.warning(f"Failed to get device size: {size_err}")

                # Validate the coordinates
                valid_coords = validate_coordinates(tap_coordinates, device_width, device_height)

                if valid_coords:
                    # Image exists with valid coordinates, tap on it
                    x, y = valid_coords
                    self.logger.info(f"Image found at valid position: ({x}, {y}), tapping...")

                    # Use the controller's tap method
                    if hasattr(self.controller, 'tap'):
                        tap_result = self.controller.tap(x, y)
                        if isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                            return {"status": "error", "message": f"Failed to tap on image: {tap_result.get('message')}"}
                        return {"status": "success", "message": f"Tapped on image: {image_filename} at position ({x}, {y})"}
                    else:
                        # Fallback to touch method if tap is not available
                        self.logger.info("Using touch method as fallback")
                        from airtest.core.api import touch
                        touch((x, y))
                        return {"status": "success", "message": f"Tapped on image: {image_filename} at position ({x}, {y})"}
                else:
                    # Image found but coordinates are invalid
                    self.logger.error(f"Image found but coordinates are invalid: {tap_coordinates}")
                    return {"status": "error", "message": f"Image found but coordinates are invalid: {tap_coordinates}"}
            else:
                # Image doesn't exist, do nothing (this is the expected behavior for "if exists")
                self.logger.info(f"Image not found: {image_filename}, skipping tap")
                return {"status": "success", "message": f"Image not found: {image_filename}, no action taken"}

        except Exception as e:
            self.logger.error(f"Error executing Tap If Image Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Tap If Image Exists action failed: {str(e)}"}
