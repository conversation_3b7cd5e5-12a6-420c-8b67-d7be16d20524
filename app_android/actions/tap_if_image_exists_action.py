import os
import traceback
import time
import math
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class TapIfImageExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Tap If Image Exists action.
        This action will tap on an image if it exists on the screen, and do nothing if it doesn't.

        Args:
            params (dict): Parameters for the action
                - image_filename (str): The filename of the reference image
                - threshold (float, optional): Similarity threshold (0.0-1.0). Default is 0.7
                - timeout (int, optional): Maximum time to wait for the image to appear in seconds. Default is 5

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
        """
        try:
            # Check if controller is available
            if not self.controller:
                return {"status": "error", "message": "Device controller not available"}

            # Get parameters
            image_filename = params.get('image_filename')
            threshold = float(params.get('threshold', 0.7))
            timeout = int(params.get('timeout', 5))

            if not image_filename:
                return {"status": "error", "message": "Image filename is required"}

            # Resolve the image path properly (EXACT same logic as Tap Image action)
            abs_path = image_filename
            if not os.path.exists(image_filename):
                # Try to resolve from reference_images directory
                try:
                    from config import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                        if os.path.exists(full_path):
                            abs_path = full_path
                            self.logger.info(f"Resolved image path to: {abs_path}")
                        else:
                            # Try directly in reference_images folder
                            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                            if os.path.exists(ref_path):
                                abs_path = ref_path
                                self.logger.info(f"Resolved image path to: {abs_path}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not import config: {e}")
                    # Fallback to default reference_images directory
                    reference_images_dir = os.path.join(os.getcwd(), 'reference_images')
                    fallback_path = os.path.join(reference_images_dir, os.path.basename(image_filename))
                    if os.path.exists(fallback_path):
                        abs_path = fallback_path
                        self.logger.info(f"Resolved image path to fallback: {abs_path}")

            # Final check if the image exists
            if not os.path.exists(abs_path):
                return {"status": "error", "message": f"Reference image not found: {image_filename}"}

            # Get absolute path for more reliable loading (same as tap_action.py)
            abs_path = os.path.abspath(abs_path)
            self.logger.info(f"Using absolute image path: {abs_path}")
            self.logger.info(f"Looking for image: {os.path.basename(abs_path)} with threshold: {threshold}")

            # Check if the controller has a find_image method
            if not hasattr(self.controller, 'find_image'):
                return {"status": "error", "message": "Device controller does not support image recognition"}

            # Try to find the image on screen using the controller's method
            match_pos = self.controller.find_image(abs_path, threshold=threshold, timeout=timeout)

            if match_pos:
                # Validate coordinates to prevent infinity or NaN values
                device_width = None
                device_height = None

                # Try to get device dimensions if available
                if hasattr(self.controller, 'get_device_size'):
                    try:
                        device_size = self.controller.get_device_size()
                        if device_size and len(device_size) == 2:
                            device_width, device_height = device_size
                    except Exception as size_err:
                        self.logger.warning(f"Failed to get device size: {size_err}")

                # Validate the coordinates
                valid_coords = validate_coordinates(match_pos, device_width, device_height)

                if valid_coords:
                    # Image exists with valid coordinates, tap on it
                    x, y = valid_coords
                    self.logger.info(f"Image found at valid position: ({x}, {y}), tapping...")

                    # Use the controller's tap method
                    if hasattr(self.controller, 'tap'):
                        tap_result = self.controller.tap(x, y)
                        if isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                            return {"status": "error", "message": f"Failed to tap on image: {tap_result.get('message')}"}
                        return {"status": "success", "message": f"Tapped on image: {image_filename} at position ({x}, {y})"}
                    else:
                        # Fallback to touch method if tap is not available
                        self.logger.info("Using touch method as fallback")
                        from airtest.core.api import touch
                        touch((x, y))
                        return {"status": "success", "message": f"Tapped on image: {image_filename} at position ({x}, {y})"}
                else:
                    # Image found but coordinates are invalid
                    self.logger.error(f"Image found but coordinates are invalid: {match_pos}")
                    return {"status": "error", "message": f"Image found but coordinates are invalid: {match_pos}"}
            else:
                # Image doesn't exist, do nothing
                self.logger.info(f"Image not found: {image_filename}, skipping tap")
                return {"status": "success", "message": f"Image not found: {image_filename}, no action taken"}

        except Exception as e:
            self.logger.error(f"Error executing Tap If Image Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Tap If Image Exists action failed: {str(e)}"}
