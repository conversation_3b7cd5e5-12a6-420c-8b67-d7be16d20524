import os
import traceback
import time
from .base_action import BaseAction
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from coordinate_validator import validate_coordinates


class TapIfTextExistsAction(BaseAction):
    def execute(self, params):
        """
        Execute the Tap If Text Exists action.
        This action will tap on text if it exists on the screen, and do nothing if it doesn't.

        Args:
            params (dict): Parameters for the action
                - text_to_find (str): The text to search for on the screen
                - timeout (int, optional): Maximum time to wait for the text to appear in seconds. Default is 10

        Returns:
            dict: Result of the action execution
                - status (str): 'success' or 'error'
                - message (str): Description of the result
        """
        try:
            # Check if controller is available
            if not self.controller:
                return {"status": "error", "message": "Device controller not available"}

            # Get parameters
            text_to_find = params.get('text_to_find')
            timeout = int(params.get('timeout', 10))

            if not text_to_find:
                return {"status": "error", "message": "Text to find is required"}

            self.logger.info(f"Looking for text: '{text_to_find}' (timeout: {timeout}s)")

            # Try to find the text using multiple methods (same approach as tap_on_text action)
            text_found = False
            tap_coordinates = None

            # METHOD 1: Try using controller's find_text method if available
            if hasattr(self.controller, 'find_text'):
                try:
                    self.logger.info("Trying controller's find_text method")
                    result = self.controller.find_text(text_to_find, timeout=timeout)
                    if result:
                        self.logger.info(f"Text found using controller's find_text method")
                        text_found = True
                        # If result contains coordinates, use them
                        if isinstance(result, (tuple, list)) and len(result) == 2:
                            tap_coordinates = result
                        else:
                            # Try to get coordinates from the result
                            tap_coordinates = (0, 0)  # Will be handled by the tap method
                except Exception as e:
                    self.logger.warning(f"Controller's find_text method failed: {e}")

            # METHOD 2: Try using Appium's find_element with partial link text
            if not text_found and hasattr(self.controller, 'driver') and self.controller.driver:
                try:
                    from selenium.webdriver.common.by import By
                    from selenium.common.exceptions import NoSuchElementException, TimeoutException
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC

                    self.logger.info("Trying to find text using Appium WebDriver")
                    
                    # Try different strategies to find text
                    strategies = [
                        (By.XPATH, f"//*[contains(text(), '{text_to_find}')]"),
                        (By.XPATH, f"//*[@text='{text_to_find}']"),
                        (By.XPATH, f"//*[@content-desc='{text_to_find}']"),
                        (By.PARTIAL_LINK_TEXT, text_to_find),
                        (By.LINK_TEXT, text_to_find)
                    ]
                    
                    element = None
                    for by, value in strategies:
                        try:
                            self.logger.info(f"Trying strategy: {by} with value: {value}")
                            wait = WebDriverWait(self.controller.driver, timeout)
                            element = wait.until(EC.presence_of_element_located((by, value)))
                            if element:
                                self.logger.info(f"Text found using strategy: {by}")
                                text_found = True
                                break
                        except (NoSuchElementException, TimeoutException):
                            continue
                        except Exception as e:
                            self.logger.warning(f"Error with strategy {by}: {e}")
                            continue
                    
                    if element and text_found:
                        # Get element location and size
                        try:
                            location = element.location
                            size = element.size
                            
                            # Calculate center coordinates
                            x = location['x'] + size['width'] // 2
                            y = location['y'] + size['height'] // 2
                            tap_coordinates = (x, y)
                            self.logger.info(f"Text element found at coordinates: ({x}, {y})")
                        except Exception as coord_err:
                            self.logger.warning(f"Could not get element coordinates: {coord_err}")
                            tap_coordinates = None

                except ImportError as e:
                    self.logger.warning(f"Selenium imports not available: {e}")
                except Exception as e:
                    self.logger.warning(f"Error using Appium WebDriver to find text: {e}")

            # METHOD 3: Try using Airtest text recognition if available
            if not text_found:
                try:
                    from airtest.core.api import text
                    from airtest.core.error import TargetNotFoundError

                    self.logger.info("Trying Airtest text recognition")
                    
                    # Use Airtest's text function to find text
                    result = text(text_to_find)
                    if result:
                        self.logger.info(f"Text found using Airtest text recognition at: {result}")
                        text_found = True
                        tap_coordinates = result

                except ImportError as e:
                    self.logger.warning(f"Airtest not available: {e}")
                except TargetNotFoundError:
                    self.logger.info("Text not found using Airtest text recognition")
                except Exception as e:
                    self.logger.warning(f"Error using Airtest text recognition: {e}")

            # If text was found, tap on it
            if text_found:
                self.logger.info(f"Text '{text_to_find}' found, attempting to tap...")
                
                try:
                    # If we have specific coordinates, use them
                    if tap_coordinates:
                        # Validate coordinates
                        device_width = None
                        device_height = None
                        
                        # Try to get device dimensions if available
                        if hasattr(self.controller, 'get_device_size'):
                            try:
                                device_size = self.controller.get_device_size()
                                if device_size and len(device_size) == 2:
                                    device_width, device_height = device_size
                            except Exception as size_err:
                                self.logger.warning(f"Failed to get device size: {size_err}")
                        
                        # Validate the coordinates
                        valid_coords = validate_coordinates(tap_coordinates, device_width, device_height)
                        
                        if valid_coords:
                            x, y = valid_coords
                            self.logger.info(f"Text found at valid position: ({x}, {y}), tapping...")
                            
                            # Use the controller's tap method
                            if hasattr(self.controller, 'tap'):
                                tap_result = self.controller.tap(x, y)
                                if isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                                    return {"status": "error", "message": f"Failed to tap on text: {tap_result.get('message')}"}
                                return {"status": "success", "message": f"Tapped on text: '{text_to_find}' at position ({x}, {y})"}
                            else:
                                return {"status": "error", "message": "No tap method available on controller"}
                        else:
                            return {"status": "error", "message": f"Text found but coordinates are invalid: {tap_coordinates}"}
                    else:
                        # Try using the controller's tap_on_text method if available
                        if hasattr(self.controller, 'tap_on_text'):
                            tap_result = self.controller.tap_on_text(text_to_find, timeout=timeout)
                            if isinstance(tap_result, dict) and tap_result.get('status') == 'success':
                                return {"status": "success", "message": f"Tapped on text: '{text_to_find}'"}
                            elif isinstance(tap_result, dict) and tap_result.get('status') == 'error':
                                return {"status": "error", "message": f"Failed to tap on text: {tap_result.get('message')}"}
                        
                        # Fallback: use action factory's tapOnText action
                        try:
                            from action_factory import ActionFactory
                            action_factory = ActionFactory(self.controller)
                            
                            tap_params = {
                                'text_to_find': text_to_find,
                                'timeout': timeout
                            }
                            
                            result = action_factory.execute_action('tapOnText', tap_params)
                            if isinstance(result, dict) and result.get('status') == 'success':
                                return {"status": "success", "message": f"Tapped on text: '{text_to_find}'"}
                            else:
                                return {"status": "error", "message": f"Failed to tap on text using action factory: {result.get('message', 'Unknown error')}"}
                        
                        except Exception as factory_err:
                            self.logger.warning(f"Could not use action factory: {factory_err}")
                            return {"status": "error", "message": f"Text found but could not tap on it: '{text_to_find}'"}
                        
                except Exception as tap_err:
                    self.logger.error(f"Error tapping on text: {tap_err}")
                    return {"status": "error", "message": f"Error tapping on text: {str(tap_err)}"}
            else:
                # Text doesn't exist, do nothing (this is the expected behavior for "if exists")
                self.logger.info(f"Text not found: '{text_to_find}', skipping tap")
                return {"status": "success", "message": f"Text not found: '{text_to_find}', no action taken"}

        except Exception as e:
            self.logger.error(f"Error executing Tap If Text Exists action: {e}")
            traceback.print_exc()
            return {"status": "error", "message": f"Tap If Text Exists action failed: {str(e)}"}
