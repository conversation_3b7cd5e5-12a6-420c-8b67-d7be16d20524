2025-06-26 21:19:58,601 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-26 21:19:58,602 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-06-26 21:19:58,602 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-26 21:19:58,602 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-06-26 21:19:58,603 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-06-26 21:19:58,603 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-26 21:19:58,603 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-06-26 21:19:58,604 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-06-26 21:19:58,604 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-06-26 21:19:58,604 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-06-26 21:19:58,605 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-06-26 21:19:58,605 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-06-26 21:19:58,605 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-06-26 21:20:00,658 - __main__ - INFO - Existing processes terminated
2025-06-26 21:20:01,625 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-06-26 21:20:01,656 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-06-26 21:20:02,017 - app - INFO - Using directories from config.py:
2025-06-26 21:20:02,017 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-06-26 21:20:02,017 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-06-26 21:20:02,017 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-06-26 21:20:02,019] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-26 21:20:02,020] INFO in database: Test_steps table schema updated successfully
[2025-06-26 21:20:02,020] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 21:20:02,020] INFO in database: Screenshots table schema updated successfully
[2025-06-26 21:20:02,020] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 21:20:02,020] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 21:20:02,021] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 21:20:02,021] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 21:20:02,021] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 21:20:02,021] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 21:20:02,021] INFO in database: Database initialized successfully
[2025-06-26 21:20:02,021] INFO in database: Checking initial database state...
[2025-06-26 21:20:02,022] INFO in database: Database state: 0 suites, 0 cases, 8383 steps, 1 screenshots, 0 tracking entries
[2025-06-26 21:20:02,023] INFO in database: === UPDATING TEST_STEPS TABLE SCHEMA ===
[2025-06-26 21:20:02,023] INFO in database: Test_steps table schema updated successfully
[2025-06-26 21:20:02,024] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 21:20:02,024] INFO in database: Screenshots table schema updated successfully
[2025-06-26 21:20:02,024] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 21:20:02,024] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 21:20:02,024] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 21:20:02,025] INFO in database: Database initialized successfully
[2025-06-26 21:20:02,025] INFO in database: === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
[2025-06-26 21:20:02,025] INFO in database: step_idx column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: action_type column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: action_params column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: action_id column already exists in execution_tracking table
[2025-06-26 21:20:02,025] INFO in database: Successfully updated execution_tracking table schema
[2025-06-26 21:20:02,026] INFO in database: === UPDATING SCREENSHOTS TABLE SCHEMA ===
[2025-06-26 21:20:02,026] INFO in database: Screenshots table schema updated successfully
[2025-06-26 21:20:02,026] INFO in database: === CLEARING EXECUTION TRACKING TABLE ===
[2025-06-26 21:20:02,026] INFO in database: Found 0 records in execution_tracking table before clearing
[2025-06-26 21:20:02,028] INFO in database: Successfully cleared execution_tracking table. Removed 0 records.
[2025-06-26 21:20:02,091] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-26 21:20:02,091] INFO in global_values_db: Global values database initialized successfully
[2025-06-26 21:20:02,091] INFO in global_values_db: Using global values from config.py
[2025-06-26 21:20:02,091] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-26 21:20:02,131] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 21:20:02,142] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11bb99940>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-26 21:20:02,142] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-06-26 21:20:02,175] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-06-26 21:20:02,207] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-06-26 21:20:04,216] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-06-26 21:20:04,216] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-06-26 21:20:05,004] INFO in appium_device_controller: Installed Appium drivers: 
[2025-06-26 21:20:05,004] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-06-26 21:20:05,798] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-06-26 21:20:05,798] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-06-26 21:20:05,798] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-06-26 21:20:05,805] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-06-26 21:20:07,822] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:07,822] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:09,832] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:09,832] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:11,838] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:11,839] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:13,848] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:13,848] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:15,855] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:15,855] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:17,865] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:17,865] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:19,872] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:19,872] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:21,882] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:21,882] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:23,892] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:23,892] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:25,898] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:25,898] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:27,905] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:27,905] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:29,916] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:29,916] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:31,923] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:31,923] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:33,934] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:33,934] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:35,944] INFO in appium_device_controller: Appium server started successfully
[2025-06-26 21:20:35,944] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-06-26 21:20:35,970] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-06-26 21:20:35,970] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-06-26 21:20:45,010] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET / HTTP/1.1" 200 -
[2025-06-26 21:20:45,210] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,211] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,212] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,212] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,214] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,215] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,215] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,217] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,220] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,220] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/actionFormManager.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,223] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,224] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,226] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,229] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,232] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /static/js/action-manager.js?v=1750939844 HTTP/1.1" 200 -
[2025-06-26 21:20:45,236] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,237] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,238] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,240] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,245] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,245] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,248] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,252] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,255] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,257] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,257] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,263] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,265] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,266] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /static/js/main.js?v=1750939844 HTTP/1.1" 200 -
[2025-06-26 21:20:45,268] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,272] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,273] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,314] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 21:20:45,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/environments HTTP/1.1" 200 -
[2025-06-26 21:20:45,322] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-06-26 21:20:45,327] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,332] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-06-26 21:20:45,335] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:20:45,337] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/settings HTTP/1.1" 200 -
[2025-06-26 21:20:45,340] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-06-26 21:20:45,346] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:20:45,349] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,352] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-06-26 21:20:45,357] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,362] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/environments/current HTTP/1.1" 200 -
[2025-06-26 21:20:45,366] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-06-26 21:20:45,368] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,375] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,382] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:20:45,387] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-06-26 21:20:45,411] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 21:20:45,418] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/recording/list HTTP/1.1" 200 -
[2025-06-26 21:20:45,436] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-06-26 21:20:45,440] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:45] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-06-26 21:20:47,066] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 21:20:47,071] INFO in appium_device_controller: Appium server is running and ready
[2025-06-26 21:20:47,071] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-26 21:20:47,071] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:47] "GET /api/devices HTTP/1.1" 200 -
[2025-06-26 21:20:48,730] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-06-26 21:20:48,733] INFO in appium_device_controller: Appium server is running and ready
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Appium server is already running and responsive
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Connection attempt 1/3
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-06-26 21:20:48,734] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-06-26 21:20:48,735] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-06-26 21:20:48,735] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-06-26 21:20:48,735] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-06-26 21:20:48,738] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8100): Max retries exceeded with url: /status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11bc27680>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-06-26 21:20:49,844] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-06-26 21:20:50,314] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:20:50,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:20:50,317] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:20:50,318] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:20:51,855] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-06-26 21:20:51,867] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8100
[2025-06-26 21:20:51,868] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.1', 'time': 'May 27 2025 13:38:32', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': None}
[2025-06-26 21:20:51,871] INFO in appium_device_controller: Appium server is already running
[2025-06-26 21:20:51,871] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-06-26 21:20:51,872] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-06-26 21:20:51,875] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'c704a6f5398459c6e333252b6cdc74a5d8c131d6', 'built': '2025-06-26 19:44:52 +1000'}}}
[2025-06-26 21:20:51,875] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-06-26 21:20:52,606] INFO in appium_device_controller: Successfully connected to iOS device
[2025-06-26 21:20:52,607] INFO in appium_device_controller: Connected with session ID: 5315029e-1226-4635-b354-ed37070aa3df
[2025-06-26 21:20:52,607] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-06-26 21:20:52,607] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-06-26 21:20:52,607] INFO in appium_device_controller: Getting device dimensions
[2025-06-26 21:20:53,526] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-06-26 21:20:53,526] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-06-26 21:20:53,531] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-06-26 21:20:53,531] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-06-26 21:20:53,531] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-06-26 21:20:53,531] INFO in appium_device_controller: iOS version: 18.0
[2025-06-26 21:20:53,531] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-06-26 21:20:53,531] INFO in appium_device_controller: Platform helpers initialization completed
[2025-06-26 21:20:53,531] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-06-26 21:20:53,531] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-26 21:20:53,537] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-26 21:20:53,537] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-26 21:20:53,538] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-26 21:20:53,539] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-26 21:20:53,539] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-26 21:20:53,540] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-26 21:20:53,540] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-26 21:20:53,541] INFO in action_factory: Registered action handler for 'wait'
[2025-06-26 21:20:53,542] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-26 21:20:53,543] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-26 21:20:53,543] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-26 21:20:53,544] INFO in action_factory: Registered action handler for 'text'
[2025-06-26 21:20:53,548] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-06-26 21:20:53,548] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-06-26 21:20:53,550] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-26 21:20:53,551] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-26 21:20:53,553] ERROR in action_factory: Error loading action handler from input_text_action: unexpected indent (input_text_action.py, line 42)
[2025-06-26 21:20:53,554] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-26 21:20:53,555] INFO in global_values_db: Global values database initialized successfully
[2025-06-26 21:20:53,555] INFO in global_values_db: Using global values from config.py
[2025-06-26 21:20:53,555] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-26 21:20:53,557] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-26 21:20:53,557] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-26 21:20:53,557] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-26 21:20:53,558] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-26 21:20:53,559] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-26 21:20:53,560] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-26 21:20:53,561] INFO in action_factory: Registered action handler for 'tap'
[2025-06-26 21:20:53,569] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-26 21:20:53,569] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-26 21:20:53,569] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-26 21:20:53,572] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-06-26 21:20:53,572] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-06-26 21:20:53,573] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-26 21:20:53,574] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-26 21:20:53,574] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-26 21:20:53,574] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-26 21:20:53,575] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-26 21:20:53,575] INFO in action_factory: Registered action handler for 'info'
[2025-06-26 21:20:53,576] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-26 21:20:53,576] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-26 21:20:53,577] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-26 21:20:53,578] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-26 21:20:53,578] INFO in action_factory: Registered action handler for 'exists'
[2025-06-26 21:20:53,580] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-26 21:20:53,581] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-26 21:20:53,581] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-26 21:20:53,582] INFO in action_factory: Registered action handler for 'test'
[2025-06-26 21:20:53,583] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-26 21:20:53,584] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-26 21:20:53,584] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-26 21:20:53,584] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'text': TextAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-26 21:20:53,584] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'test': TestAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-26 21:20:53,585] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-26 21:20:53,585] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-06-26 21:20:53,586] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-06-26 21:20:53,591] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-06-26 21:20:53,595] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-06-26 21:20:53,596] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 21:20:53,596] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-06-26 21:20:53,596] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:20:55,025] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 21:20:55,025] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 21:20:55,313] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:20:55,314] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:20:55,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:20:55,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:20:55,945] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:55] "POST /api/device/connect HTTP/1.1" 200 -
[2025-06-26 21:20:56,964] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 21:20:56,964] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:20:58,388] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 21:20:58,388] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 21:20:58,389] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:20:58] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750936845312_4t4fq6pvw_1750936845312_4t4fq6pvw&t=1750936856960 HTTP/1.1" 200 -
[2025-06-26 21:21:00,313] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:00,314] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:00] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:00,318] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:00,319] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:00] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:02,084] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:02] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:21:05,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:05,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:05] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:05,319] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:05,320] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:05] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:10,314] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:10,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:10] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:10,319] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:10,320] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:10] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:11,511] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:11] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:21:13,196] INFO in player: Executing action: {'type': 'tapIfImageExists', 'timestamp': 1750936871506, 'image_filename': 'product-share-ip14.png', 'threshold': 0.7, 'timeout': 5}
[2025-06-26 21:21:13,196] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 21:21:13,196] INFO in player: DEBUG: Player instance ID: 4760124256, has current_test_idx: True
[2025-06-26 21:21:13,197] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 21:21:13,197] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 21:21:13,197] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 21:21:13,197] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 21:21:13,197] INFO in player: ========== ACTION TYPE: tapIfImageExists ==========
[2025-06-26 21:21:13,197] INFO in player: ========== ACTION ID:  ==========
[2025-06-26 21:21:13,197] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:21:13,197] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:21:13,197] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:21:13,197] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:21:13,197] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 0
[2025-06-26 21:21:13,197] INFO in database: DEBUG: filename: unknown, action_type: tapIfImageExists
[2025-06-26 21:21:13,197] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 21:21:13,212] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1348, in execute_action
    track_test_execution(

[2025-06-26 21:21:13,212] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:21:13,213] INFO in database: DEBUG: action_params: {"type": "tapIfImageExists", "timestamp": 1750936871506, "image_filename": "product-share-ip14.png",...
[2025-06-26 21:21:13,213] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 0, unknown)
[2025-06-26 21:21:13,213] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 21:21:13,214] INFO in database: Created execution tracking for test unknown (idx: 0, step: 0): status=running, retry=0/0
[2025-06-26 21:21:13,216] INFO in database: Updated step information in test_steps table: Step 0: tapIfImageExists
[2025-06-26 21:21:13,217] INFO in player: Tracked execution in database: test_idx=0, step_idx=0, action_type=tapIfImageExists, action_id=
[2025-06-26 21:21:13,218] INFO in player: Skipping device connection verification for better performance
[2025-06-26 21:21:13,218] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-26 21:21:13,218] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-26 21:21:13,218] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-26 21:21:13,218] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-26 21:21:13,218] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-26 21:21:13,218] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-26 21:21:13,218] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'wait'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'text'
[2025-06-26 21:21:13,219] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-26 21:21:13,219] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-26 21:21:13,220] ERROR in action_factory: Error loading action handler from input_text_action: unexpected indent (input_text_action.py, line 42)
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-26 21:21:13,220] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'tap'
[2025-06-26 21:21:13,220] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-26 21:21:13,221] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-26 21:21:13,221] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-26 21:21:13,221] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-26 21:21:13,221] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'info'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-26 21:21:13,221] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'exists'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-26 21:21:13,221] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-26 21:21:13,222] INFO in action_factory: Registered action handler for 'test'
[2025-06-26 21:21:13,222] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-26 21:21:13,222] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-26 21:21:13,222] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-26 21:21:13,222] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'text': TextAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-26 21:21:13,222] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'test': TestAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-26 21:21:13,223] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-26 21:21:13,224] INFO in player: Forwarding tapIfImageExists action to ActionFactory with params: {'image_filename': 'product-share-ip14.png', 'threshold': 0.7, 'timeout': 5}
[2025-06-26 21:21:13,224] INFO in action_factory: Requested action type: 'tapIfImageExists', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifElseSteps', 'info', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapIfTextExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-06-26 21:21:13,224] INFO in action_factory: Action parameters before env resolution: {'image_filename': 'product-share-ip14.png', 'threshold': 0.7, 'timeout': 5}
[2025-06-26 21:21:13,224] INFO in tap_if_image_exists_action: Resolved image path to: /Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png
[2025-06-26 21:21:13,224] INFO in tap_if_image_exists_action: Using absolute image path: /Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png
[2025-06-26 21:21:13,224] INFO in tap_if_image_exists_action: Looking for image: product-share-ip14.png with threshold: 0.7
[2025-06-26 21:21:13,224] INFO in tap_if_image_exists_action: Using Airtest Template and exists() method (primary method)
[2025-06-26 21:21:13,224] INFO in tap_if_image_exists_action: Created Template object: Template(/Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png)
[2025-06-26 21:21:13,231] WARNING in tap_if_image_exists_action: Airtest method failed: 'No devices added.'
[2025-06-26 21:21:13,231] INFO in tap_if_image_exists_action: Airtest not initialized, trying fallback methods
[2025-06-26 21:21:13,231] INFO in tap_if_image_exists_action: Trying controller's find_image method
[2025-06-26 21:21:13,231] INFO in appium_device_controller: Finding image: path=/Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png, threshold=0.7, timeout=5
[2025-06-26 21:21:13,231] INFO in appium_device_controller: Using absolute image path: /Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png
[2025-06-26 21:21:13,232] INFO in appium_device_controller: Using resolved image path: /Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png
[2025-06-26 21:21:13,232] INFO in appium_device_controller: Created template with threshold: 0.7
[2025-06-26 21:21:13,234] WARNING in appium_device_controller: Error during image matching (attempt 1): 'No devices added.'
[2025-06-26 21:21:13,737] WARNING in appium_device_controller: Error during image matching (attempt 2): 'No devices added.'
[2025-06-26 21:21:14,239] WARNING in appium_device_controller: Error during image matching (attempt 3): 'No devices added.'
[2025-06-26 21:21:14,745] WARNING in appium_device_controller: Error during image matching (attempt 4): 'No devices added.'
[2025-06-26 21:21:15,248] WARNING in appium_device_controller: Error during image matching (attempt 5): 'No devices added.'
[2025-06-26 21:21:15,313] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:15,313] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:15,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:15,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:15,752] WARNING in appium_device_controller: Error during image matching (attempt 6): 'No devices added.'
[2025-06-26 21:21:16,254] WARNING in appium_device_controller: Error during image matching (attempt 7): 'No devices added.'
[2025-06-26 21:21:16,757] WARNING in appium_device_controller: Error during image matching (attempt 8): 'No devices added.'
[2025-06-26 21:21:17,262] WARNING in appium_device_controller: Error during image matching (attempt 9): 'No devices added.'
[2025-06-26 21:21:17,764] WARNING in appium_device_controller: Error during image matching (attempt 10): 'No devices added.'
[2025-06-26 21:21:18,267] INFO in appium_device_controller: Image '/Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png' not found after 10 attempts within timeout of 5 seconds
[2025-06-26 21:21:18,268] INFO in tap_if_image_exists_action: Image not found using controller's find_image method
[2025-06-26 21:21:18,268] INFO in tap_if_image_exists_action: Trying OpenCV directly for image recognition: /Users/<USER>/Documents/automation-tool/reference_images/product-share-ip14.png
[2025-06-26 21:21:18,268] INFO in tap_if_image_exists_action: Device dimensions: 393x852
[2025-06-26 21:21:18,673] INFO in tap_if_image_exists_action: Original screenshot size: 1178x2556
[2025-06-26 21:21:18,673] INFO in tap_if_image_exists_action: Using original screenshot dimensions: 1178x2556
[2025-06-26 21:21:18,767] INFO in tap_if_image_exists_action: Template dimensions: 151x185
[2025-06-26 21:21:18,767] INFO in tap_if_image_exists_action: Using OpenCV threshold: 0.5 (original: 0.7)
[2025-06-26 21:21:19,055] INFO in tap_if_image_exists_action: Template matching with TM_CCOEFF_NORMED: 0.9999999403953552
[2025-06-26 21:21:19,269] INFO in tap_if_image_exists_action: Template matching with TM_CCORR_NORMED: 1.0
[2025-06-26 21:21:19,479] INFO in tap_if_image_exists_action: Template matching with TM_SQDIFF_NORMED: 0.9999999942405702
[2025-06-26 21:21:19,479] INFO in tap_if_image_exists_action: Best template matching result: 1.0 with method TM_CCORR_NORMED (threshold: 0.5)
[2025-06-26 21:21:19,479] INFO in tap_if_image_exists_action: Found image at (1102, 276) in original screenshot using OpenCV
[2025-06-26 21:21:19,479] INFO in tap_if_image_exists_action: Image found at valid position: (1102, 276), tapping...
[2025-06-26 21:21:19,480] INFO in appium_device_controller: Tapping at coordinates: (1102, 276)
[2025-06-26 21:21:19,480] INFO in appium_device_controller: Using mobile: tap for iOS at (1102, 276)
[2025-06-26 21:21:20,314] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:20,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:20] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:20,318] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:20,319] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:20] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:20,735] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 21:21:20,735] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 21:21:20,735] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 21:21:20,736] INFO in player: DEBUG: Player instance ID: 4760124256, has current_test_idx: True
[2025-06-26 21:21:20,736] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 21:21:20,736] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 21:21:20,736] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 21:21:20,736] INFO in player: ========== ACTION TYPE: tapIfImageExists ==========
[2025-06-26 21:21:20,736] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:21:20,736] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:21:20,736] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:21:20,736] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:21:20,736] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 0
[2025-06-26 21:21:20,736] INFO in database: DEBUG: filename: unknown, action_type: tapIfImageExists
[2025-06-26 21:21:20,736] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 21:21:20,737] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 3028, in execute_action
    track_test_execution(

[2025-06-26 21:21:20,737] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:21:20,737] INFO in database: DEBUG: action_params: {"type": "tapIfImageExists", "timestamp": 1750936871506, "image_filename": "product-share-ip14.png",...
[2025-06-26 21:21:20,737] INFO in database: DEBUG: execution_result: {"status": "success", "message": "Tapped on image: product-share-ip14.png at position (1102, 276)"}
[2025-06-26 21:21:20,737] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 0, unknown)
[2025-06-26 21:21:20,737] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 21:21:20,738] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 0): status=passed, retry=0/0
[2025-06-26 21:21:20,739] INFO in database: Updated step information in test_steps table: Step 0: tapIfImageExists
[2025-06-26 21:21:20,741] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=0, action_type=tapIfImageExists, status=passed
[2025-06-26 21:21:21,744] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 21:21:21,745] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-06-26 21:21:21,745] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:21:23,160] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 21:21:23,160] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 21:21:23,165] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:21:23,165] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:21:23,165] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 1
[2025-06-26 21:21:23,165] INFO in database: DEBUG: filename: unknown, action_type: tapIfImageExists
[2025-06-26 21:21:23,165] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 21:21:23,166] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3608, in execute_single_action
    track_test_execution(

[2025-06-26 21:21:23,166] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:21:23,166] INFO in database: DEBUG: action_params: {"type": "tapIfImageExists", "timestamp": 1750936871506, "image_filename": "product-share-ip14.png",...
[2025-06-26 21:21:23,166] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 1, unknown)
[2025-06-26 21:21:23,166] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 21:21:23,167] INFO in database: Created execution tracking for test unknown (idx: 0, step: 1): status=in_progress, retry=0/0
[2025-06-26 21:21:23,168] INFO in database: Updated step information in test_steps table: Step 1: tapIfImageExists
[2025-06-26 21:21:23,169] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:23] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 21:21:23,173] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 21:21:23,174] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:21:24,557] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_212123/screenshots/latest.png
[2025-06-26 21:21:24,557] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 21:21:24,557] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 21:21:24,558] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:24] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750936845312_4t4fq6pvw_1750936845312_4t4fq6pvw&t=1750936883171 HTTP/1.1" 200 -
[2025-06-26 21:21:25,314] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:25,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:25,317] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:25,318] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:30,315] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:30,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:30,319] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:30,320] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:35,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:35,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:35] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:35,320] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:35,321] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:35] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:40,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:40,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:40,319] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:40,320] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:40] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:45,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:45,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:45,319] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:45,320] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:45] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:46,041] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:46] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:21:50,315] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:50,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:50] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:21:55,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:21:55,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:21:55] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:00,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:00,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:00] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:05,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:05,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:05] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:07,545] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:07] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:22:10,315] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:10,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:10] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:15,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:15,318] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:15] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:17,852] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:17] "GET /api/reference_images HTTP/1.1" 200 -
[2025-06-26 21:22:19,647] INFO in player: Executing action: {'type': 'ifElseSteps', 'timestamp': 1750936937847, 'condition_type': 'exists', 'condition': {'locator_type': 'accessibility_id', 'locator_value': 'Add to bag', 'timeout': 10}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}}
[2025-06-26 21:22:19,647] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-06-26 21:22:19,647] INFO in player: DEBUG: Player instance ID: 4760124256, has current_test_idx: True
[2025-06-26 21:22:19,647] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 21:22:19,647] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-06-26 21:22:19,647] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-06-26 21:22:19,647] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 21:22:19,647] INFO in player: ========== ACTION TYPE: ifElseSteps ==========
[2025-06-26 21:22:19,647] INFO in player: ========== ACTION ID:  ==========
[2025-06-26 21:22:19,647] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:22:19,647] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:22:19,647] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:22:19,647] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:22:19,647] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 1
[2025-06-26 21:22:19,647] INFO in database: DEBUG: filename: unknown, action_type: ifElseSteps
[2025-06-26 21:22:19,648] INFO in database: DEBUG: status: running, retry: 0/0, in_progress: True
[2025-06-26 21:22:19,648] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 1348, in execute_action
    track_test_execution(

[2025-06-26 21:22:19,648] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:22:19,648] INFO in database: DEBUG: action_params: {"type": "ifElseSteps", "timestamp": 1750936937847, "condition_type": "exists", "condition": {"locat...
[2025-06-26 21:22:19,648] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 1, unknown)
[2025-06-26 21:22:19,649] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 21:22:19,649] INFO in database: Created execution tracking for test unknown (idx: 0, step: 1): status=running, retry=0/0
[2025-06-26 21:22:19,651] INFO in database: Updated step information in test_steps table: Step 1: ifElseSteps
[2025-06-26 21:22:19,652] INFO in player: Tracked execution in database: test_idx=0, step_idx=1, action_type=ifElseSteps, action_id=
[2025-06-26 21:22:19,653] INFO in player: Skipping device connection verification for better performance
[2025-06-26 21:22:19,653] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-26 21:22:19,653] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-26 21:22:19,653] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-26 21:22:19,653] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-26 21:22:19,653] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-26 21:22:19,653] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-26 21:22:19,653] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'wait'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'text'
[2025-06-26 21:22:19,654] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-26 21:22:19,654] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-26 21:22:19,655] ERROR in action_factory: Error loading action handler from input_text_action: unexpected indent (input_text_action.py, line 42)
[2025-06-26 21:22:19,655] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-26 21:22:19,655] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-26 21:22:19,655] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'tap'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-26 21:22:19,656] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-26 21:22:19,656] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-26 21:22:19,656] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-26 21:22:19,656] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-26 21:22:19,656] INFO in action_factory: Registered action handler for 'info'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-26 21:22:19,657] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'exists'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'test'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-26 21:22:19,657] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-26 21:22:19,657] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'text': TextAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-26 21:22:19,658] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'test': TestAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-26 21:22:19,659] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-26 21:22:19,659] INFO in player: Forwarding ifElseSteps action to ActionFactory with params: {'condition_type': 'exists', 'condition': {'locator_type': 'accessibility_id', 'locator_value': 'Add to bag', 'timeout': 10}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}, 'else_action': None}
[2025-06-26 21:22:19,659] INFO in action_factory: Requested action type: 'ifElseSteps', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifElseSteps', 'info', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapIfTextExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-06-26 21:22:19,659] INFO in action_factory: Action parameters before env resolution: {'condition_type': 'exists', 'condition': {'locator_type': 'accessibility_id', 'locator_value': 'Add to bag', 'timeout': 10}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}, 'else_action': None}
[2025-06-26 21:22:19,659] INFO in if_else_steps_action: Executing If Steps with condition type: exists
[2025-06-26 21:22:19,659] INFO in if_else_steps_action: Then action: {'type': 'tap', 'x': 0, 'y': 0}
[2025-06-26 21:22:19,660] INFO in if_else_steps_action: Checking condition of type: exists
[2025-06-26 21:22:19,660] INFO in if_else_steps_action: Condition parameters: {'locator_type': 'accessibility_id', 'locator_value': 'Add to bag', 'timeout': 10}
[2025-06-26 21:22:19,660] INFO in if_else_steps_action: Then action: {'type': 'tap', 'x': 0, 'y': 0}
[2025-06-26 21:22:19,660] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-06-26 21:22:19,660] INFO in global_values_db: Global values database initialized successfully
[2025-06-26 21:22:19,661] INFO in global_values_db: Using global values from config.py
[2025-06-26 21:22:19,661] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300}
[2025-06-26 21:22:19,662] INFO in if_else_steps_action: Checking if element exists with accessibility_id: Add to bag, timeout=10s
[2025-06-26 21:22:19,662] INFO in appium_device_controller: Finding element with accessibility_id: Add to bag, timeout=10s
[2025-06-26 21:22:20,314] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:20,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:20] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:21,203] INFO in if_else_steps_action: Condition check result: Element exists = True
[2025-06-26 21:22:21,203] INFO in action_factory: Registered basic actions: tap, wait
[2025-06-26 21:22:21,203] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'multiStep'
[2025-06-26 21:22:21,203] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'swipe'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'getParam'
[2025-06-26 21:22:21,203] INFO in action_factory: Registered action handler for 'wait'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'text'
[2025-06-26 21:22:21,204] INFO in action_factory: Special case: Registering tap_if_text_exists_action.py as 'tapIfTextExists'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'tapIfTextExists'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'waitTill'
[2025-06-26 21:22:21,204] INFO in action_factory: Registered action handler for 'hookAction'
[2025-06-26 21:22:21,205] ERROR in action_factory: Error loading action handler from input_text_action: unexpected indent (input_text_action.py, line 42)
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'setParam'
[2025-06-26 21:22:21,205] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'clickImage'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'tap'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'ifElseSteps'
[2025-06-26 21:22:21,205] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-06-26 21:22:21,205] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-06-26 21:22:21,205] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-06-26 21:22:21,205] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'launchApp'
[2025-06-26 21:22:21,206] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'info'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'waitElement'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'compareValue'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-06-26 21:22:21,206] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'exists'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'clickElement'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'randomData'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'getValue'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'test'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'restartApp'
[2025-06-26 21:22:21,206] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-06-26 21:22:21,206] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'tapIfTextExists', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'ifElseSteps', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'tap': TapAction
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'wait': WaitAction
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-06-26 21:22:21,206] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'text': TextAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'tapIfTextExists': TapIfTextExistsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'ifElseSteps': IfElseStepsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'info': InfoAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-06-26 21:22:21,207] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-06-26 21:22:21,208] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-06-26 21:22:21,208] INFO in action_factory: Handler for 'test': TestAction
[2025-06-26 21:22:21,208] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-06-26 21:22:21,208] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-06-26 21:22:21,208] INFO in if_else_steps_action: Condition met, executing then action: tap
[2025-06-26 21:22:21,208] INFO in if_else_steps_action: Then action is tap with method: coordinates
[2025-06-26 21:22:21,208] INFO in if_else_steps_action: Executing tap at coordinates: (0, 0)
[2025-06-26 21:22:21,208] INFO in if_else_steps_action: Executing then action with action factory: tap
[2025-06-26 21:22:21,208] INFO in if_else_steps_action: Then action parameters: {'type': 'tap', 'x': 0, 'y': 0}
[2025-06-26 21:22:21,208] INFO in action_factory: Requested action type: 'tap', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifElseSteps', 'info', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapIfTextExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-06-26 21:22:21,208] INFO in action_factory: Action parameters before env resolution: {'type': 'tap', 'x': 0, 'y': 0}
[2025-06-26 21:22:21,208] INFO in appium_device_controller: Tapping at coordinates: (0, 0)
[2025-06-26 21:22:21,208] INFO in appium_device_controller: Using mobile: tap for iOS at (0, 0)
[2025-06-26 21:22:22,373] INFO in if_else_steps_action: Then action result: {'status': 'success', 'message': 'Successfully tapped at (0, 0) using mobile: tap'}
[2025-06-26 21:22:22,373] INFO in player: Skipping delay after action execution for better performance
[2025-06-26 21:22:22,373] WARNING in player: Could not take screenshot after action: cannot access local variable 'i' where it is not associated with a value
[2025-06-26 21:22:22,373] INFO in player: DEBUG: Using local_test_idx: 0 for tracking completion
[2025-06-26 21:22:22,374] INFO in player: DEBUG: Player instance ID: 4760124256, has current_test_idx: True
[2025-06-26 21:22:22,374] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-06-26 21:22:22,374] INFO in player: DEBUG: Global current_test_idx.value: 0
[2025-06-26 21:22:22,374] INFO in player: ========== PLAYER COMPLETING ACTION WITH TEST_IDX: 0 ==========
[2025-06-26 21:22:22,374] INFO in player: ========== ACTION TYPE: ifElseSteps ==========
[2025-06-26 21:22:22,374] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:22:22,374] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-06-26 21:22:22,374] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:22:22,374] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:22:22,374] INFO in database: DEBUG: suite_id: unknown, test_idx: 0, step_idx: 1
[2025-06-26 21:22:22,374] INFO in database: DEBUG: filename: unknown, action_type: ifElseSteps
[2025-06-26 21:22:22,374] INFO in database: DEBUG: status: passed, retry: 0/0, in_progress: False
[2025-06-26 21:22:22,375] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/utils/player.py", line 3028, in execute_action
    track_test_execution(

[2025-06-26 21:22:22,375] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:22:22,375] INFO in database: DEBUG: action_params: {"type": "ifElseSteps", "timestamp": 1750936937847, "condition_type": "exists", "condition": {"locat...
[2025-06-26 21:22:22,375] INFO in database: DEBUG: execution_result: {"status": "success", "message": "Condition true, executed action: Successfully tapped at (0, 0) usi...
[2025-06-26 21:22:22,375] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (unknown, 0, 1, unknown)
[2025-06-26 21:22:22,375] INFO in database: DEBUG: Existing entry found: True
[2025-06-26 21:22:22,376] INFO in database: Updated execution tracking for test unknown (idx: 0, step: 1): status=passed, retry=0/0
[2025-06-26 21:22:22,377] INFO in database: Updated step information in test_steps table: Step 1: ifElseSteps
[2025-06-26 21:22:22,378] INFO in player: Tracked execution completion in database: test_idx=0, step_idx=1, action_type=ifElseSteps, status=passed
[2025-06-26 21:22:23,383] INFO in appium_device_controller: Using current report screenshots directory: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_212123/screenshots
[2025-06-26 21:22:23,383] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-06-26 21:22:23,383] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_212123/screenshots/placeholder.png (save_debug=False)
[2025-06-26 21:22:23,383] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:22:24,788] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_212123/screenshots/latest.png
[2025-06-26 21:22:24,788] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-06-26 21:22:24,789] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-06-26 21:22:24,792] INFO in database: ========== TRACKING TEST EXECUTION ==========
[2025-06-26 21:22:24,792] INFO in database: DEBUG: RECEIVED test_idx: 0 (type: <class 'int'>)
[2025-06-26 21:22:24,792] INFO in database: DEBUG: suite_id: , test_idx: 0, step_idx: 2
[2025-06-26 21:22:24,793] INFO in database: DEBUG: filename: unknown, action_type: ifElseSteps
[2025-06-26 21:22:24,793] INFO in database: DEBUG: status: in_progress, retry: 0/0, in_progress: True
[2025-06-26 21:22:24,793] INFO in database: DEBUG: Called from:   File "/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/app.py", line 3608, in execute_single_action
    track_test_execution(

[2025-06-26 21:22:24,793] INFO in database: DEBUG: Converted test_idx to int: 0
[2025-06-26 21:22:24,793] INFO in database: DEBUG: action_params: {"type": "ifElseSteps", "timestamp": 1750936937847, "condition_type": "exists", "condition": {"locat...
[2025-06-26 21:22:24,793] INFO in database: DEBUG: Checking for existing entry with query: SELECT id FROM execution_tracking WHERE suite_id = ? AND test_idx = ? AND step_idx = ? AND filename = ? and params: (, 0, 2, unknown)
[2025-06-26 21:22:24,794] INFO in database: DEBUG: Existing entry found: False
[2025-06-26 21:22:24,794] INFO in database: Created execution tracking for test unknown (idx: 0, step: 2): status=in_progress, retry=0/0
[2025-06-26 21:22:24,796] INFO in database: Updated step information in test_steps table: Step 2: ifElseSteps
[2025-06-26 21:22:24,797] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:24] "POST /api/action/execute HTTP/1.1" 200 -
[2025-06-26 21:22:24,800] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-06-26 21:22:24,800] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-06-26 21:22:25,315] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:25,315] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:25] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:26,180] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/testsuite_execution_20250626_212123/screenshots/latest.png
[2025-06-26 21:22:26,180] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-06-26 21:22:26,180] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-06-26 21:22:26,181] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:26] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1750936845312_4t4fq6pvw_1750936845312_4t4fq6pvw&t=1750936944798 HTTP/1.1" 200 -
[2025-06-26 21:22:30,316] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:30,317] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:30] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-06-26 21:22:35,315] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-06-26 21:22:35,316] INFO in _internal: 127.0.0.1 - - [26/Jun/2025 21:22:35] "GET /api/reports/latest HTTP/1.1" 200 -
